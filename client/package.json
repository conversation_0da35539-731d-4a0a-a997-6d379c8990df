{
  "name": "commoncare",
  "version": "0.0.1",
  "description": "An Uncommon Level of Care",
  "productName": "CommonCare",
  "author": "tyler hall <{49353034}+{ha6755ad}@users.noreply.github.com>",
  "private": true,
  "scripts": {
    "dev": "QENV=development /opt/homebrew/bin/npx --always-spawn quasar dev",
    "build-dev": "quasar build",
    "lint": "eslint --ext .js,.ts,.vue ./",
    "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore",
    "test": "echo \"No test specified\" && exit 0",
    "prepare-build": "npm ci --production=false",
    "build": "npm run prepare-build && quasar build"
  },
  "dependencies": {
    "@feathersjs/authentication-client": "^5.0.31",
    "@feathersjs/socketio-client": "^5.0.31",
    "@mapbox/mapbox-gl-draw": "^1.5.0",
    "@moovio/moov-js": "^0.0.16",
    "@quasar/extras": "^1.16.15",
    "@stripe/connect-js": "^3.3.20",
    "@stripe/stripe-js": "^5.5.0",
    "@turf/turf": "^7.2.0",
    "@ucans/ucans": "^0.12.0",
    "awesome-phonenumber": "^7.2.0",
    "deep-object-diff": "^1.1.9",
    "feathers-batch": "^1.1.1",
    "feathers-hooks-common": "^8.2.1",
    "feathers-pinia": "^4.5.4",
    "localforage": "^1.10.0",
    "mapbox-gl": "^3.9.2",
    "md-editor-v3": "^5.2.1",
    "node-html-markdown": "^1.3.0",
    "parse-domain": "^8.2.2",
    "pdfobject-vue": "^0.0.4",
    "pinia": "^2.3.0",
    "port-numbers": "^8.0.21",
    "qrcode-svg": "^1.1.0",
    "quasar": "^2.17.6",
    "rrule": "^2.8.1",
    "sanitize-html": "^2.16.0",
    "showdown": "^2.1.0",
    "socket.io-client": "^4.8.1",
    "symbol-auth-client": "^0.0.9",
    "symbol-syntax-utils": "^0.0.22",
    "uninstall": "^0.0.0",
    "vue": "^3.5.13",
    "vue-router": "^4.5.0"
  },
  "devDependencies": {
    "@quasar/app-vite": "^2.0.6",
    "@types/node": "^22.10.6",
    "@typescript-eslint/eslint-plugin": "^8.20.0",
    "@typescript-eslint/parser": "^8.20.0",
    "autoprefixer": "^10.4.20",
    "eslint": "^9.18.0",
    "eslint-config-prettier": "^10.0.1",
    "eslint-plugin-vue": "^9.32.0",
    "prettier": "^3.4.2",
    "typescript": "^5.7.3"
  },
  "engines": {
    "node": "^18 || ^16 || ^14.19",
    "npm": ">= 6.13.4",
  }
}
