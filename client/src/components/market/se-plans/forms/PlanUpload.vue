<template>
  <div class="_fw">
    <div class="q-pa-md flex items-center">
      <div class="tw-six">Plan Year Override: </div>
      <plan-year-picker v-model="year"></plan-year-picker>
    </div>
    <csv-upload
        exact-headers
        :max-file-size="100000000"
        :headers="Object.keys(planHeaders).join(',')"
        @ready="uploadPlans"
        :example-data="exampleData"
        :response="response"
    >
      <template v-slot:response="scope">
        <div v-if="scope.response.errs?.length">
          <div class="q-pa-sm tw-six text-red">Errors</div>
          <div class="__tbl">
            <table>
              <thead>
              <tr>
                <th>ID</th>
                <th>Error</th>
              </tr>
              </thead>
              <tbody>
              <tr class="__errrow" v-for="(obj, i) in scope.response.errs" :key="`err-${i}`">
                <td>{{obj.id}}</td>
                <td>{{obj.message}}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="q-py-md __res">
          <div class="q-pa-sm tw-six">Added Results ({{scope.response?.data?.length || 0}})</div>
          <table>
            <thead>
            <tr>
              <th>Plan ID</th>
              <th>Plan Name</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(res, i) in (scope.response.data || []).slice(0, 100)" :key="`res-${i}`">
              <td>{{res.plan_id}}</td>
              <td>{{res.name}}</td>
            </tr>
            </tbody>
          </table>
          <div v-if="scope.response.data?.length > 100">+ {{scope.response.data.length - 100}} More</div>
        </div>
      </template>
    </csv-upload>
  </div>
</template>

<script setup>
  import PlanYearPicker from 'components/plans/utils/PlanYearPicker.vue';
  import CsvUpload from 'components/common/uploads/csv/CsvUpload.vue';

  import {planHeaders} from '../utils/plans';
  import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';
  import {ref} from 'vue';

  const exampleData = Object.keys(planHeaders).map(k => {
    return { header: k, ex: ' - ' };
  });

  const response = ref({});
  const year = ref(new Date().getFullYear())

  const uploadPlans = async (val, meta) => {
    const res = await axiosFeathers().post('/se-plans', val, {
      params: {
        runJoin: { plan_upload: { ...meta, year: year.value, method: 'plans' } },
        core: restCore()
      }
    });
    response.value = res.data;
  };
</script>

<style lang="scss" scoped>

  .__tbl {
    width: 100%;
    overflow-x: scroll;
  }

  table {
    width: 100%;
    border-collapse: collapse;

    tr {
      th {
        padding: 4px 8px;
        font-weight: 600;
        color: var(--ir-mid);
        font-size: .8rem;
        border-bottom: .3px solid var(--ir-light);
      }
      td {
        font-size: .9rem;
        padding: 4px 8px;
        border-bottom: solid .2px var(--ir-light);
      }

      .__errrow {
        td {
          border-bottom-color: var(--q-ir-red);
        }
      }

      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }
  }

  .__res {
    max-height: 500px;
    overflow-y: scroll;
  }
</style>
