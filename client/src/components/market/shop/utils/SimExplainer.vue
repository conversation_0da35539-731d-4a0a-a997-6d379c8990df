<template>
  <div class="_fw">
    <div class="pw1">
      <div class="font-1-1-4r">AI is cool and all, but how do you know you can trust this data?</div>
      <div class="font-1-1-4r tw-six">Here's our simulation process</div>
    </div>
    <div class="row q-py-md">
      <div class="col-12 col-md-4 q-py-md pw1" v-for="(step, i) in steps" :key="`step-${i}`">
        <div class="__c">
          <div class="__label">{{ step.label }}</div>
          <div class="__caption" v-html="step.caption"></div>
          <div class="__arrow" v-if="$q.screen.lt.md || (i+1) % 3 !== 0">
            <q-icon :name="`mdi-arrow-${$q.screen.lt.md ? 'down' : 'right'}`" class="text-lg"></q-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="q-pb-lg pw1 font-1-1-4r alt-font">
      <div class="row">
        <div class="__c" style="height: auto">
          <div class="font-1-1-4r tw-six">The result:</div>
          <div class="tw-four _fw font-1r q-py-xs">
            <div>- Millions of real data points per simulation</div>
            <div class="q-py-xs">- Unprecedented vision of which policy really suits you</div>
            <div>- Ability to keep more net money in your pocket</div>
          </div>

        </div>
      </div>
    </div>


    <div class="font-1-1-4r alt-font pw1 _fw">
      <div class="q-pt-md tw-five">There are some qualitative issues we don't look at here.</div>

      <div class="__list">
        <template v-for="(item, i) in oranges" :key="`orange-${i}`">
          <div>
            <div>{{ item.label }}</div>
            <div v-html="item.caption" :class="activeOrange === i || printMode ? '' : '__off'"></div>
            <div v-if="activeOrange !== i && !printMode">
              <q-chip color="transparent" dense square clickable @click="activeOrange = i">
                <span class="q-mr-sm tw-six font-7-8r">Read More</span>
                <q-icon color="primary" name="mdi-menu-down"></q-icon>
              </q-chip>
            </div>
          </div>
        </template>
      </div>
    </div>

  </div>
</template>

<script setup>

  import {ref} from 'vue';

  const props = defineProps({
    printMode: Boolean
  })

  const activeOrange = ref(-1);

  const steps = [
    {
      label: 'Actuarial Data',
      caption: 'We take millions of real medical bills from data sets like <a class="__a" target="_blank" href="https://meps.ahrq.gov/mepsweb/data_stats/download_data_files_detail.jsp?cboPufNumber=HC-233">this</a> and simulate normal bills for each member of your household - based on age, zip, and risk profile - repeated for 100 years.'
    },
    {
      label: 'Plan Data',
      caption: 'Then we take the facts of each plan - premium, deductible, coinsurance, and out of pocket maximums - and apply it to each individual simulated bill for each household member.'
    },
    {
      label: 'Ranking Data',
      caption: 'We rank the plans based on outcomes - so you can see which plan is most likely to cost you the least over time, but also see how well each plan does under best to worst case scenarios.'
    }
  ]

  const oranges = [
    {
      label: 'Negotiated medical bills',
      caption: 'Price transparency laws have confirmed what cash-paying patients have long understood: large insurance networks often pay more than individuals can negotiate on their own. At CommonCare, we help members access discounted rates on care, medications, and labs - but not all insurance plans permit you to bypass their contracted network pricing. While allowing for negotiation would favor plans with lower premiums and more flexible networks, we’ve chosen not to factor that into our simulations. For consistency, all plans are assumed to pay the same billed price.'
    },
    {
      label: 'Care access, quality, and incentives',
      caption: 'Having access to a high quality physician can significantly reduce your cost of care and care experience. We are huge advocates of membership based care because it often contains health issues rather than cascading them as often occurs in refer-ology driven care. Again, the simulation doesn\'t assume any type of plan results in more effective treatment - even though private care outside often does. We can\'t simulate qualitative outcomes in an unbiased manner. We will tell you without apology that private care is better than insurance dictated care.<br><br>Of course, if you want a doctor who goes to work containing all of your medical needs and costs - you can and should explore one of our direct primary care physicians asap.'
    },
    {
      label: 'Denials',
      caption: 'Most people have heard the claim that two-thirds of U.S. bankruptcies are linked to medical bills. What fewer realize is that three out of four of those individuals had health insurance at the time. Claim denials are a risk under any plan. Health shares, while not legally obligated to pay like insurance, tend to be more transparent and accessible when it comes to their claims process. In our experience, insurance “guarantees” often relate more to solvency protections than to individual claim outcomes. <br><br>Health shares operate on a smaller scale and lack government backing, but their communication around what is or isn’t eligible tends to be more straightforward. On the other hand, we’ve found that even with full insurance contracts in hand, it’s nearly impossible—for our AI models or even attorneys—to determine with certainty whether a specific procedure will be covered and under what conditions.<br><br>For this simulation, we have not attempted to model claim denial risk. That’s a real-world uncertainty across all options. The best way to reduce that risk is to verify coverage directly before undergoing a procedure.'
    }
  ]
</script>

<style lang="scss" scoped>

  .__a {
    text-decoration: none !important;
    color: var(--q-accent) !important;
    font-weight: 600 !important;
  }

  .__c {
    padding: 25px 15px;
    border-radius: 12px;
    border: solid 2px var(--ir-light);
    //background: linear-gradient(135deg, var(--q-a0), var(--ir-bg2), var(--q-a0));
    position: relative;
    height: 100%;
  }

  .__label {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .__caption {
    font-size: 1rem;

  }

  .__arrow {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(100%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }

  @media screen and (max-width: 1023px) {
    .__arrow {
      top: 100%;
      right: 50%;
      transform: translate(50%, 10%);
    }
  }

  .__list {
    margin-top: 25px;
    padding: 0 1vw 20px 1vw;
    //border-radius: 1.5vw;
    //background: white;
    //box-shadow: 18px -18px 48px var(--q-p0), -18px 18px 48px var(--q-a0);

    > div {
      padding: 15px 0;

      > div {
        &:first-child {
          font-size: 1.25rem;
          font-weight: 600;
          font-family: var(--alt-font);
          color: var(--q-p5);
          margin-bottom: 10px;
        }

        &:nth-child(2) {
          font-size: 1rem;
          transition: all .3s;
        }
      }
    }
  }

  .__off {
    max-height: 25px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
