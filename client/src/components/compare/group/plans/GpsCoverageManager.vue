<template>
  <div class="row items-center">
    <q-chip color="ir-bg2" clickable @click="addPlan">
      <span class="q-mr-sm">Add Plan</span>
      <q-icon name="mdi-plus-circle" color="primary"></q-icon>
    </q-chip>
    <ai-upload-chip
        color="ir-bg2"
        v-model:gps="form" :save="save"></ai-upload-chip>
  </div>
  <q-tab-panels class="_panel" :model-value="!!coverageId" animated>
    <q-tab-panel class="_panel" :name="true">
      <div class="row items-center">
        <q-btn dense flat icon="mdi-chevron-left" color="primary" @click="coverageId = undefined"></q-btn>
        <q-chip color="white">
          <span>Plan:&nbsp;&nbsp;<b>{{ coverageId?.split('_').join(' - ') }}</b></span>
        </q-chip>
      </div>

      <div class="_fw q-pa-sm">
        <div class="_frm">
          <div class="_lbl">Name</div>
          <div class="_bod">
            <q-input dense filled v-model="form.coverages[coverageId].name"
                     @update:modelValue="autoSave(`coverages.${coverageId}.name`, $event)"></q-input>
          </div>
          <div class="_lbl">Carrier Name</div>
          <div class="_bod">
            <q-input dense filled v-model="form.coverages[coverageId].carrierName"
                     @update:modelValue="autoSave(`coverages.${coverageId}.carrierName`, $event)"></q-input>
          </div>
          <div class="_lbl">Carrier Logo</div>
          <div class="_bod">

            <carrier-logo-picker
                v-model="form.coverages[coverageId].carrierLogo"
                @update:model-value="autoSave(`coverages.${coverageId}.carrierLogo`, $event)"
            ></carrier-logo-picker>
          </div>
          <div class="_lbl">Deductible</div>
          <div class="_bod" @click="editing = 'ded'">
            <deductible-form
                :model-value="form.coverages[coverageId].deductible"
                @update:model-value="setDed(coverageId, $event)"
            ></deductible-form>
          </div>

          <div class="_lbl">Premium</div>
          <div class="_bod">
            <div v-if="editing !== 'premium'" class="_fw cursor-pointer" @click="editing = 'premium'">
              <rate-table :model-value="form.coverages[coverageId]" :age="def_age"></rate-table>
            </div>

            <q-slide-transition>
              <premium-form
                  v-if="editing === 'premium'"
                  allow-estimate
                  :model-value="form.coverages[coverageId].premium"
                  @update:modelValue="setPremium(coverageId, $event)"
              ></premium-form>
            </q-slide-transition>
          </div>
          <div class="_lbl">MOOP</div>
          <div class="_bod" @click="editing = 'moop'">
            <limit-form
                oop
                :model-value="form.coverages[coverageId].moop"
                @update:model-value="setMoop(coverageId, $event)"
            ></limit-form>

          </div>
          <div class="_lbl">Coinsurance</div>
          <div class="_bod" @click="editing = 'coins'">
            <div class="font-7-8r">Amount participant pays after deductible & before max out of pocket ie: 20%</div>
            <money-input suffix="%" prefix="" :model-value="form.coverages[coverageId].coinsurance?.amount"
                         @update:model-value="setCoins(coverageId, $event)"
                         class="_fw mw300"></money-input>
          </div>
        </div>
      </div>
    </q-tab-panel>
    <q-tab-panel class="_panel" :name="false">

      <div class="row items-start">
        <div class="col-12 col-md-4 q-pa-sm" v-if="!covs.length">
          <div class="_fw flex flex-center h100">
            <div class="font-1r text-italic">No Plans Added</div>
          </div>
        </div>
        <div class="col-12 col-md-4 q-py-md pw1" v-for="(cov, i) in covs" :key="`cov-${i}`">
          <div class="__c">
            <div class="row justify-end">
              <q-btn dense flat size="sm" icon="mdi-eye" color="accent"></q-btn>
              <q-btn @click="setCoverageId(cov.id || cov._id)" dense flat size="sm" icon="mdi-pencil-box"
                     color="ir-mid"></q-btn>
              <remove-proxy-btn @remove="removeCoverage(cov.id)" dense flat size="sm" icon="mdi-delete"
                            color="red"></remove-proxy-btn>
            </div>
            <div class="flex items-center">
              <q-img v-if="cov.carrierLogo" :src="byId.bySource(cov.id)" class="h20 w20 q-mr-sm"
                     fit="contain"></q-img>
              <span class="font-7-8r tw-six text-ir-mid">{{ cov.carrierName || '' }}</span>
            </div>
            <div class="font-1r tw-five text-ir-deep">{{ cov.name }}</div>
            <q-separator class="q-my-sm"></q-separator>
            <rate-table :model-value="cov" :age="def_age"></rate-table>

            <div class="row q-pt-sm">
              <q-chip color="ir-bg2" clickable @click="$copyTextToClipboard(cov.id, 'ID Copied')">
                <q-icon name="mdi-content-copy" color="accent" class="q-mr-sm"></q-icon>
                <span>{{ cov.id }}</span>
              </q-chip>
            </div>
          </div>
        </div>
      </div>

    </q-tab-panel>
  </q-tab-panels>
</template>

<script setup>

  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import RateTable from 'components/coverages/cards/RateTable.vue';
  import CarrierLogoPicker from 'components/coverages/utils/CarrierLogoPicker.vue';
  import PremiumForm from 'components/coverages/forms/PremiumForm.vue';
  import AiUploadChip from 'components/compare/utils/AiUploadChip.vue';
  import DeductibleForm from 'components/coverages/forms/DeductibleForm.vue';
  import LimitForm from 'components/plans/forms/premiums/LimitForm.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  import {computed, nextTick, reactive, ref} from 'vue';
  import {HForm} from 'src/utils/hForm';
  import {useGps} from 'stores/gps';
  import {manageFindUploads} from 'components/utils/uploads/file-manager';
  import {_get} from 'symbol-syntax-utils';
  import {idGet} from 'src/utils/id-get';
  import {costLimits} from 'components/plans/utils';
  import {$copyTextToClipboard} from 'src/utils/global-methods';
  import {useRoute, useRouter} from 'vue-router';


  const gpsStore = useGps();
  const router = useRouter();
  const route = useRoute();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true }
  })

  const editing = ref('')

  const coverageId = ref();
  const cFn = (defs) => {
    return {
      type: 'mm',
      covered: 'group',
      name: 'My Employer Health Plan',
      deductible: {
        ...costLimits.deductible
      },
      coinsurance: {
        amount: 40
      },
      moop: {
        ...costLimits.moop
      },
      ...defs,
      id: coverageId.value || new Date().getTime().toString() + '-' + 0,
    }
  }

  const { item: gps } = idGet({
    store: gpsStore,
    value: computed(() => props.modelValue)
  })

  const { form, save } = HForm({
    store: gpsStore,
    value: gps
  })

  const covs = computed(() => Object.keys(form.value.coverages || {}).map(a => form.value.coverages[a]))

  const { byId } = manageFindUploads({ sources: reactive({ data: covs }), paths: ['carrierLogo'] })

  const patchObj = ref({});
  const saveTo = ref()
  const autoSave = (path, val) => {
    clearTimeout(saveTo.value);
    const v = val || val === 0 || val === false ? val : _get(form.value, path);
    patchObj.value = { ...patchObj.value, [path]: v }
    emit('update:model-value', form.value)
    saveTo.value = setTimeout(async () => {
      const obj = { ...patchObj.value }
      patchObj.value = {};
      if (form.value._id) {
        const res = await gpsStore.patch(gps.value._id, { $set: obj })
            .catch(err => {
              console.error(`Error patching gps: ${err.message}`)
              patchObj.value = { ...obj, ...patchObj.value }
            })
        emit('update:model-value', res)
      }
    }, 3000)
  }

  const setPremium = async (covId, val) => {
    form.value.coverages = { ...form.value.coverages, [covId]: { ...form.value.coverages[covId], id: covId, premium: val } }
    nextTick(async () => {
      if (!form.value._id) await save()
      autoSave(`coverages.${covId}.premium`, val)
    })
  }
  const setDed = async (covId, val) => {
    form.value.coverages = { ...form.value.coverages, [covId]: { ...form.value.coverages[covId], id: covId, deductible: val } }
    nextTick(async () => {
      if (!form.value._id) await save()
      autoSave(`coverages.${covId}.deductible`, val)
    })
  }
  const setMoop = async (covId, val) => {
    form.value.coverages = { ...form.value.coverages, [covId]: { ...form.value.coverages[covId], id: covId, moop: val } }
    nextTick(async () => {
      if (!form.value._id) await save()
      autoSave(`coverages.${covId}.moop`, val)
    })
  }
  const setCoins = async (covId, val) => {
    form.value.coverages = {
      ...form.value.coverages,
      [covId]: { ...form.value.coverages[covId], id: covId, coinsurance: { amount: val } }
    }
    nextTick(async () => {
      if (!form.value._id) await save()
      autoSave(`coverages.${covId}.coinsurance`, { amount: val })
    })
  }

  const removeCoverage = (id) => {
    delete form.value.coverages[id]
    gpsStore.patch(form.value._id, { $unset: { [`coverages.${id}`]: '' } })
  }

  const toggleCoverageId = (val) => {
    if (!val) coverageId.value = undefined
  }

  const addPlan = async () => {
    const cvg = cFn();
    form.value.groupCompare = true;
    form.value.coverages = { ...form.value.coverages, [cvg.id]: { ...cvg } }
    if (!form.value._id) await save()
    if (gps.value._id) {
      gpsStore.patchInStore(gps.value._id, {
        coverages: {
          ...gps.value.coverages,
          [form.value.id]: form.value
        }
      })
      gpsStore.patch(gps.value._id, { $set: { [`coverages.${cvg.id}`]: cvg } })
    }
    coverageId.value = cvg.id;
    router.push({ ...route, params: { ...route.params, gpsId: form.value._id } })
  }

  const setCoverageId = (id) => {
    coverageId.value = id;
  }

  const def_age = ref(40)

</script>

<style lang="scss" scoped>

  .__c {
    border-radius: 10px;
    box-shadow: 0 2px 6px var(--ir-light);
    background: white;
    padding: 10px;
  }

  ._bod {
    max-width: 600px;
  }
</style>
