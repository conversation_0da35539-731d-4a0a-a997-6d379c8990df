<template>
  <div class="_fw">
    <div class="_fw q-py-sm font-1r" v-if="gps.simStats && (gps.simStats.selected_count || gps.simStats[`${prefix}selected_count`])">
      <div>{{$possiblyPlural('p', gps.simStats.selected_count + prefix ? gps.simStats[`${prefix}selected_count`] : 0, 'erson has', 'eople have')}} selected their preferred plan</div>
    </div>
    <table class="__grp">
      <thead>
      <tr>
        <th></th>
        <th>Current Plan</th>
        <th>Employee Choice</th>
        <template v-if="usePtc">
          <th>PTC</th>
          <th>Net Cost</th>
        </template>
        <th>Best Alternative</th>
      </tr>
      </thead>
      <tbody>
      <template v-for="(res, i) in groupResults.rows" :key="`res-${i}`">
        <tr class="__label">
          <td>{{ res.label }}</td>
          <td></td>
          <td></td>
          <template v-if="usePtc">
            <td></td>
            <td></td>
          </template>
          <td class="__alt"></td>
        </tr>
        <tr v-for="k in ['', '_avg']" :key="`k-${i}-${k}`" :class="`__grp_res${k}`">
          <td>{{ k === '_avg' ? 'Per Employee' : 'Total' }}</td>
          <td>{{ res[`current${k}`] }}</td>
          <td :class="`__first ${usePtc ? '' : '__last'}`">{{ res[`best${k}`] }}</td>
          <template v-if="usePtc">
            <td>{{ res[`ptc${k}`] }}</td>
            <td class="__last">{{ res[`net${k}`] }}</td>
          </template>
          <td class="__alt">{{ res[[`alt${k}`]] }}</td>
        </tr>
      </template>
      </tbody>
    </table>
  </div>
</template>

<script setup>

  import {computed} from 'vue';
  import {$possiblyPlural, dollarString} from 'src/utils/global-methods';

  const props = defineProps({
    prefix: { default: '' },
    usePtc: Boolean,
    gps: {
      required: true, default: () => {
        return {}
      }
    }
  })

  const groupResults = computed(() => {
    const { currentStats, simStats } = props.gps || {};
    if (!currentStats || !simStats) return [];

    const pre = props.prefix || ''
    const add_pre = pre === 'inactive_';
    const getSim = (p) => {
      const base = simStats[p];
      if(!add_pre) return base;
      return base + (simStats[`${pre}${p}`] || 0);
    }
    const simPremium = getSim('premium') - (getSim('selected_premium_delta') || 0) + (getSim('selected_premium') || 0)

    const ptc = getSim('ptc')
    const net = Math.max(0, simPremium - ptc);
    const altPremium = getSim('altPremium')
    const altSpend = getSim('altSpend')
    const altOOP = altSpend - altPremium;
    const simSpend = getSim('spend') - (getSim('selected_spend_delta') || 0) + (getSim('selected_spend') || 0);

    const count = getSim('count');

    const rows = [
      {
        label: 'Premium (Avg)',
        current: currentStats.spendPremium,
        current_avg: currentStats.spendPremium / currentStats.spendCount,
        best: simPremium,
        best_avg: simPremium / count,
        ptc: -1 * ptc,
        ptc_avg: -1 * ptc / count,
        net: net,
        net_avg: net / count,
        alt: altPremium,
        alt_avg: altPremium / count
      },
      {
        label: 'Out Of Pocket (Avg)',
        current: currentStats.spend - currentStats.spendPremium,
        current_avg: (currentStats.spend - currentStats.spendPremium) / currentStats.spendCount,
        best: simSpend - simPremium,
        best_avg: (simSpend - simPremium) / count,
        ptc: null,
        ptc_avg: null,
        net: simSpend - simPremium,
        net_avg: (simSpend - simPremium) / count,
        alt: altOOP,
        alt_avg: altOOP / count
      },
      {
        label: 'Total Cost (Premium + OOP)',
        current: currentStats.spend,
        current_avg: currentStats.spend / currentStats.spendCount,
        best: simSpend,
        best_avg: simSpend / count,
        ptc: -1 * ptc,
        ptc_avg: -1 * ptc / count,
        net: Math.max(0, simSpend - ptc),
        net_avg: Math.max(0, simSpend - ptc) / count,
        alt: altSpend,
        alt_avg: altSpend / count
      }
    ];

    return {
      rows: rows.map(row => {
        const formatted = { label: row.label };
        for (const key in row) {
          if (key !== 'label') {
            formatted[key] = dollarString(row[key], '$', 0, '-');
          }
        }
        return formatted;
      })
    }
  });
</script>

<style lang="scss" scoped>
  .__grp {
    width: 100%;
    border-collapse: collapse;


    tr {
      th {
        padding: 8px;
        text-align: left;
        font-size: .95rem;
        font-weight: 600;
        color: var(--ir-deep);
        white-space: nowrap;
      }

      td {
        font-family: var(--alt-font);
        padding: 8px;
        text-align: left;
        font-weight: 500;
        color: var(--ir-text);
        font-size: 1rem;

        &:first-child {
          padding-left: 17px;
          font-weight: 500;
        }
      }
      &:last-child {
        td {
          border-bottom: none;
        }
      }
      .__first {
        border-left: solid 2px var(--q-primary);
      }

      .__last {
        background: var(--q-p0) !important;
        border-right: solid 2px var(--q-accent);
      }

      .__alt {
        background: var(--q-a0) !important;
      }
    }

    .__label {
      td {
        background: var(--q-p0);
        font-weight: 600 !important;
        color: var(--q-p5);
        white-space: nowrap;

        &:first-child {
          padding-left: 12px;
          border-radius: 8px 0 0 8px;
        }

        &:last-child {
          border-radius: 0 8px 8px 0;
        }
      }
    }

    .__grp_res {
      td {
        background: var(--ir-bg2);
        color: var(--ir-text);
      }
    }

    .__grp_res_avg {
      td {
        color: var(--ir-deep);
      }
    }
  }

</style>
