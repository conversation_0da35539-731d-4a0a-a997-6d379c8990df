<template>
  <div class="_fw">
    <table class="__tax_table">
      <thead>
      <tr>
        <th></th>
        <th>Current Plan</th>
        <th>Employee Directed Plan</th>
        <th v-if="usePtc">Employee Directed + PTC</th>
        <th>Alternative Plan</th>
      </tr>
      </thead>
      <tbody>
      <template v-for="(k, i) in Object.keys(groupResults.taxStats)" :key="`tax-${i}`">
        <tr>
          <td>{{groupResults.taxStats[k].label}}</td>
          <td>{{dollarString(groupResults.taxStats[k].c, '$', 0)}}</td>
          <td>{{dollarString(groupResults.taxStats[k].q, '$', 0)}}</td>
          <td v-if="usePtc">{{dollarString(groupResults.taxStats[k].nq, '$', 0)}}</td>
          <td>{{dollarString(groupResults.taxStats[k].alt, '$', 0)}}</td>
        </tr>
      </template>
      </tbody>
    </table>
  </div>
</template>

<script setup>
  import {computed} from 'vue';
  import {dollarString} from 'src/utils/global-methods';

  const props = defineProps({
    prefix: { default: '' },
    usePtc: Boolean,
    gps: {
      required: true, default: () => {
        return {}
      }
    }
  })

  const groupResults = computed(() => {
    const { currentStats, simStats } = props.gps || {};
    if (!currentStats || !simStats) return [];

    const pre = props.prefix || ''
    const add_pre = pre === 'inactive_';
    const getSim = (p, fn) => {
      const base = simStats[p];
      if(!add_pre) return base;
      const v = base + (simStats[`${pre}${p}`] || 0);
      if(fn) return fn(v);
      return v;
    }

    const simPremium = getSim('premium') - (getSim('selected_premium_delta') || 0) + (getSim('selected_premium') || 0)
    const simSpend = getSim('spend') - (getSim('selected_spend_delta') || 0) + (getSim('selected_spend') || 0);
    const simTaxSavings = getSim('tax_savings') - (getSim('selected_tax_savings_delta') || 0) + (getSim('selected_tax_savings') || 0);
    const count = getSim('count')
    const selectedRatio = (getSim('selected_count') || 0) / count

    const simTr = (simStats.tax_rate * (1-selectedRatio)) + (simStats.selected_tax_rate * selectedRatio)
    const preTr = add_pre ? ((simStats[`${pre}tax_rate`] || 0) * (1 - selectedRatio)) + ((simStats[`${pre}selected_tax_rate`] || 0) * selectedRatio) : 0;
    const simAdj = simTr * simStats.count/(simStats[`${pre}count`] || count)
    const preAdj = preTr * (simStats[`${pre}count`]/(simStats.count || count))
    const simTaxRate = simAdj + preAdj;

    const altPremium = getSim('altPremium')
    const altSpend = getSim('altSpend')
    const ptc = getSim('ptc')

    const er_savings = simPremium * .0765
    const cur_er_savings = currentStats.spendPremium * .0765
    const ee_savings = simPremium * (.0765 + simTaxRate);
    const cur_ee_savings = currentStats.spendPremium * (.0765 + simTaxRate);
    const oop = simSpend - simPremium
    const cur_oop = currentStats.spend - currentStats.spendPremium
    const taxStats = {
      'premium': {
        label: 'Premium',
        c: currentStats.spendPremium,
        q: simPremium,
        nq: simPremium,
        alt: altPremium
      },
      'oop': {
        label: 'Out of Pocket',
        c: cur_oop,
        q: oop,
        nq: oop,
        alt: altSpend - altPremium
      },
      'er_savings': {
        label: 'Employer Tax Savings',
        c: -1 * cur_er_savings,
        q: -1 * er_savings,
        nq: 0,
        alt: 0
      },
      'ee_savings': {
        label: 'Employee Tax Savings',
        c: -1 * cur_ee_savings,
        q: -1 * ee_savings + (oop * simTaxRate),
        nq: -1 * oop * simTaxRate,
        alt: 0
      },
      'ptc': {
        label: 'Tax Credits',
        c: 0,
        q: 0,
        nq: -1 * ptc,
        alt: 0
      },
      'net_premium': {
        label: 'Net Premium',
        c: currentStats.spendPremium - cur_ee_savings - cur_er_savings,
        q: simPremium - simTaxSavings,
        nq: simPremium - ptc,
        alt: altPremium
      },
      'net': {
        label: 'Total Net Cost',
        c: currentStats.spend - (cur_ee_savings + cur_er_savings),
        q: simSpend - (ee_savings + er_savings + oop * simTaxRate),
        nq: simSpend - ptc - (oop * simTaxRate),
        alt: altSpend
      }
    }


    return {
      taxStats
    }
  });
</script>

<style lang="scss" scoped>

  .__tax_table {
    width: 100%;
    border-collapse: collapse;
    tr {
      th {
        padding: 8px;
        text-align: left;
        font-size: .95rem;
        font-weight: 600;
        color: var(--ir-deep);
        white-space: nowrap;
      }

      td {
        font-family: var(--alt-font);
        padding: 8px;
        text-align: left;
        font-weight: 500;
        color: var(--ir-text);
        font-size: 1rem;
        border-left: solid 2px var(--ir-mid);

        &:first-child {
          padding-left: 17px;
          font-weight: 500;
          border-left: none;

        }
      }

      &:last-child {
        td {
          border-bottom: none;
        }
      }


      &:nth-child(3){
        td {
          color: var(--q-s7);
          font-weight: 600;
          background: var(--q-s0);
        }
      }
      &:nth-child(4){
        td {
          color: var(--q-s5);
          font-weight: 600;
        }
      }
      &:nth-child(5){
        td {
          color: var(--q-a7);
          font-weight: 600;
          background: var(--q-a0);
        }
      }
      &:nth-child(6) {
        td {
          font-weight: 600;
          color: var(--q-p5);
        }
      }
      &:nth-child(7) {
        td {
          font-weight: 600;
          color: var(--q-p6);
          background: var(--q-p0);
        }
      }
    }
  }
</style>
