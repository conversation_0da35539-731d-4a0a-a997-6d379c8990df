<template>
  <div class="_fw">
    <div class="row items-center">
      <q-chip color="transparent" dense square clickable @click="tab = 'link'">
        <span :class="tab === 'link' ? 'tw-six text-primary' : ''">Link</span>
        <q-icon name="mdi-link" color="primary" class="q-ml-sm"></q-icon>
      </q-chip>
      <div>|</div>
      <q-chip color="transparent" dense square clickable @click="tab = 'email'">
        <span :class="tab === 'email' ? 'tw-six text-primary' : ''">Email</span>
        <q-icon name="mdi-email" color="accent" class="q-ml-sm"></q-icon>
      </q-chip>
      <div>|</div>
      <q-chip color="transparent" dense square clickable @click="tab = 'print'">
        <span :class="tab === 'print' ? 'tw-six text-primary' : ''">Print</span>
        <q-icon name="mdi-printer" color="secondary" class="q-ml-sm"></q-icon>
      </q-chip>
    </div>
    <q-tab-panels class="_panel" v-model="tab" animated transition-next="jump-up" transition-prev="jump-down">
      <q-tab-panel class="_panel" name="link">

        <div class="_fw q-py-md">
          <q-input clearable @click="setSearch('')" :model-value="search" @update:modelValue="setSearch">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
          <table class="__list">
            <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Link</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(ee, i) in ees" :key="`ee-${i}`">
              <td>{{ ee.firstName + ' ' + ee.lastName }}</td>
              <td>{{ ee.email }}</td>
              <td>
                <input readonly @click="$copyTextToClipboard(getLink(ee), 'Link Copied')" class="__scinp"
                       :value="getLink(ee)">
              </td>
            </tr>
            </tbody>
          </table>
        </div>


      </q-tab-panel>
      <q-tab-panel class="_panel" name="email">
        <census-table
            :limit="1000"
            :store="gpsStore"
            service-path="gps"
            :required="['firstName', 'lastName', 'email']"
            :id="gps._id"
            :update="updateCensus"
            no-add
        ></census-table>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="print">
        <div class="row justify-center">
          <q-btn v-if="!printing && !loading" icon="mdi-printer" dense flat no-caps @click="print(gps.employees.length > 50)"></q-btn>
          <q-spinner v-else color="primary" size="20px"></q-spinner>
        </div>

        <template v-if="!printView">
        <div class="q-py-lg _fa alt-font" id="PrintResults">

          <div class="q-pb-md flex items-center">
            <q-img :src="logo" class="h30 w30 q-mr-sm"></q-img>
            <div class="font-1-1-4r tw-six text-ir-mid alt-font">Employee Directed Health Plan Comparative Analysis</div>
          </div>
          <div class="__c">
            <div class="__t">
              <div>Group Totals</div>
              <div>Summary of plan costs current vs employee directed insurance<span v-if="usePtc">, insurance + PTC,</span> and alternative plans.</div>
              <div v-if="prefix.includes('inactive')" class="font-1r tw-five text-ir-deep">(All employees are included in the analysis - including those not currently participating)</div>
            </div>
            <group-totals :prefix="prefix" :gps="gps" :use-ptc="usePtc"></group-totals>
          </div>
          <div class="__c">
            <div class="__t">
              <div>Tax Comparison</div>
              <div>Comparison of tax advantages<span v-if="usePtc"> - current & qualified plan structure means tax-free premiums but no PTC. PTC makes premiums post-tax, but provides a tax credit.</span> Alternatives are usually post-tax.</div>
            </div>
            <tax-comparison :prefix="prefix" :gps="gps" :use-ptc="usePtc"></tax-comparison>
          </div>
          <div class="__c">
            <div class="__t">
              <div>Individual Totals</div>
              <div>Summary by individual. Best insurance plan was auto-selected based on our analysis, individuals can choose from over 150 plans we analyzed.</div>
            </div>
            <individual-totals :prefix="prefix" v-model:loading="loading" print-mode no-search no-pagination :gps="gps" :use-ptc="usePtc"></individual-totals>
          </div>

          <div class="__c">
            <sim-explainer print-mode></sim-explainer>
          </div>

          <div class="q-py-lg q-px-md font-3-4r"><i>What would our legal team want us to say here? Probably something like: This is just an illustration. We’ve made a deep effort to capture accurate data regarding coverages, tax incentives/credits, and assumptions. The employee data is only as accurate as what you (or your team) provided to us. That said, this simulation offers a strong representation of how an employee-directed approach can significantly impact your company. In real life, adjustments will happen.
            <br><br>
            No, you can’t use this document to avoid paying taxes.
            <br><br>
            Also, if you’re in the middle of a medical emergency, why are you looking at a health plan simulation? Call 911. Or don’t - we’re not here to tell you how to handle emergencies. But this probably isn’t the best time for benefits modeling.</i></div>
        </div>
        </template>
        <template v-else>
          <div class="_fa">
            <div class="row q-px-md q-py-sm">
              <q-btn dense flat icon="mdi-close" @click="printView = false"></q-btn>
            </div>
            <template v-if="!tooBig">
            <file-preview :model-value="{ url: pdfUrl, info: { name: `${org.dba || org.name || gps.orgName || 'Company'}_employee_directed_plan`, type: 'application/pdf' } }"></file-preview>
            </template>
            <template v-else>
              <div class="q-pa-lg font-1r text-italic">File too large to preview - it was opened or downloaded automatically</div>
            </template>

          </div>
        </template>
      </q-tab-panel>
    </q-tab-panels>


  </div>
</template>

<script setup>
  import logo from 'src/assets/commoncare_icon.svg'
  import CensusTable from 'pages/landing/sm-er/results/CensusTable.vue';
  import GroupTotals from 'components/compare/group/results/tables/GroupTotals.vue';
  import TaxComparison from 'components/compare/group/results/tables/TaxComparison.vue';
  import IndividualTotals from 'components/compare/group/results/tables/IndividualTotals.vue';
  import SimExplainer from 'components/market/shop/utils/SimExplainer.vue';
  import FilePreview from 'components/common/uploads/pages/FilePreview.vue';

  import {useGps} from 'stores/gps';
  import {LocalStorage} from 'symbol-auth-client';
  import {computed, ref} from 'vue';
  import {$copyTextToClipboard, getRootDomain} from 'src/utils/global-methods';
  import {useRouter} from 'vue-router';
  import {handlePrint} from 'components/plans/docs/utils/print';

  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';


  const gpsStore = useGps();
  const orgStore = useOrgs();
  const router = useRouter();

  const props = defineProps({
    prefix: { default: '' },
    gps: {
      required: true, default: () => {
        return {}
      }
    },
    usePtc: Boolean
  })

  const { item:org } = idGet({
    store: orgStore,
    value: computed(() => props.gps.org)
  })

  const search = ref('');
  const ees = computed(() => {
    return props.gps.employees.filter(a => {
      const name = `${a.firstName} ${a.lastName} ${a.email || ''}`
      return name.toLowerCase().includes(search.value.toLowerCase())
    })
  })

  const searchTo = ref();
  const setSearch = (val) => {
    clearTimeout(searchTo.value);
    searchTo.value = setTimeout(() => {
      search.value = val;
    }, 250)
  }

  const tab = ref('link');

  const getLink = (ee) => {
    const { href } = router.resolve({
      name: 'compare-share',
      params: { gpsId: props.gps._id, shopId: ee.sim }
    });
    return getRootDomain() + href;
  }


  const loading = ref(false);
  const updateCensus = async (employees, data, auto) => {
    const args = [{ employees }]
    if (!auto) {
      const dr = await gpsStore[method](...args)
          .catch(err => {
            console.error(`Error adding dr: ${err.message}`);
            loading.value = false;
          })
      if (dr._id) {
        router.push({ ...route, params: { ...route.params, gpsId: dr._id } })
        LocalStorage.setItem('gps', dr._id);
        changes.value = true;
        if (!auto) emit('update:model-value', dr)
      }
    } else if (gps.value._id) gpsStore.patchInStore(...args)
  }

  const { print, printing, pdfUrl, printView, tooBig } = handlePrint(ref('PrintResults'))

</script>

<style lang="scss" scoped>
  table {
    width: 100%;
    border-collapse: collapse;

    tr {
      th {
        color: var(--ir-mid);
      }

      td, th {
        border-bottom: solid 1px var(--ir-light);
        padding: 8px;
        font-size: .9rem;
        text-align: left;
      }
      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }
  }

  .__h {
    padding: 6px 10px !important;
    font-weight: 600;
    color: var(--ir-mid);
  }
  .__scinp {
    border: none;
    padding: 0px;
    width: 100%;
    cursor: pointer;
  }


  .__c {
    width: 100%;
    padding: 50px 1vw;
    background: white;
    border-bottom: solid 4px var(--ir-light);
  }
  .__t {
    padding-bottom: 30px;
    > div {
      font-family: var(--alt-font);
      color: var(--ir-deep);

      &:first-child {
        font-size: 1.24rem;
        font-weight: 600;
      }
      &:last-child {
        font-size: 1rem;
        font-weight: 400;
      }
    }

  }

  @media print {
    .__c {
      page-break-inside: avoid;  /* Prevent breaking inside these blocks */
      break-inside: avoid;       /* Modern fallback */
    }

    #PrintResults {
      display: block;
      page-break-before: auto;
      page-break-after: auto;
    }

    .__c + .__c {
      page-break-before: auto;
    }

    body {
      margin: 0;
    }

    /* Optional: remove overflow or flex layout issues */
    .flex, .q-py-lg, ._fa {
      overflow: visible !important;
      height: auto !important;
    }
  }
</style>
