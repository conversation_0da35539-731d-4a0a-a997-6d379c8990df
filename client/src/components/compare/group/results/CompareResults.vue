<template>
  <div class="_fw pw2">
    <q-tab-panels class="_panel" v-model="page" animated transition-next="jump-up" transition-prev="jump-down">
      <q-tab-panel class="_panel" name="results">
        <template v-if="!ready.ready">
          <div class="row justify-center q-py-lg">
            <div class="_fw mw800">
              <div class="text-center font-1r tw-five">
                Fix these issues to run simulation:
              </div>
              <ul>
                <li v-if="ready.message">{{ ready.message }}</li>
                <li v-if="ready.premiumMessage">{{ ready.premiumMessage }}</li>
                <li v-if="ready.coverageMessage">{{ ready.coverageMessage }}</li>
              </ul>
            </div>
          </div>
        </template>
        <template v-else>
          <template v-if="progress.value || notRun.length === gps.employees.length">
            <div class="row justify-center q-py-md">
              <ul class="font-1r tw-five _fw mw800">
                <li v-if="ready.message">{{ ready.message }}</li>
                <li v-if="ready.premiumMessage">{{ ready.premiumMessage }}</li>
                <li v-if="ready.coverageMessage">{{ ready.coverageMessage }}</li>
              </ul>
            </div>
            <div class="row justify-center q-py-md">
              <div class="w600 mw100">
                <div class="text-center font-1r">Once your employee data is accurate, run a market simulation for each
                  employee to see how each plan performs.
                </div>
                <div class="row justify-center q-pt-md">
                  <q-btn @click="runCompare" :disable="loading" no-caps rounded :color="loading ? 'p1' : 'primary'"
                         glossy>
                    <span v-if="!loading" class="q-mr-sm tw-six text-white">Run Simulations</span>
                    <span v-else class="q-mr-sm tw-six text-p10">Running Simulations</span>
                    <ai-logo :dark="!loading" opaque size="20px"></ai-logo>
                  </q-btn>
                </div>
                <div class="row justify-center q-pt-lg" v-if="progressing">
                  <div class="__bar">
                    <div class="__fill" :style="{ width: `${progress}%` }"></div>
                  </div>
                </div>
                <div class="q-pt-sm text-primary alt-font font-1-1-4r tw-six">{{dollarString(progress, '', 2)}}%</div>
              </div>
            </div>
          </template>
          <template v-else-if="simProgress">
            <div class="_fw q-py-lg">
              <div class="font-1r text-center row justify-center">
                <div class="w700 mw100">
                  Your state is a federal exchange state and the request for data has rate limits that we exceeded with
                  this request.
                  <div class="q-pt-md">
                    We have finished <span
                      class="text-primary tw-six alt-font">{{ simProgress }} of {{ gps.employees?.length }}</span>
                    employees.
                  </div>
                  <div class="q-pt-md">
                    Trying again in <span class="text-accent tw-six alt-font">{{ simCountdown }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="_fw">
              <div class="_fw q-pb-lg q-px-sm">
                <div class="font-1r text-deep tw-five">Like what you see?&nbsp;&nbsp;&nbsp;
                  <q-chip color="primary" clickable @click="openStart">
                    <span class="tw-six text-white q-mr-sm">Start Your Plan</span>
                    <q-icon color="white" name="mdi-chevron-right"></q-icon>
                  </q-chip>
                </div>

              </div>
              <div class="row items-center">
                <q-chip dense :clickable="!loading" color="transparent" @click="recalc">
                  <span class="q-mr-sm tw-five">Re-sum</span>
                  <q-icon v-if="!loading" color="primary" name="mdi-calculator"></q-icon>
                  <q-spinner v-else color="primary"></q-spinner>
                </q-chip>
                <div>|</div>
                <q-chip dense color="transparent" clickable>
                  <span class="q-mr-sm tw-five">Re-simulate</span>
                  <q-icon v-if="!loading" color="secondary" name="mdi-refresh"></q-icon>
                  <q-spinner v-else color="secondary"></q-spinner>
                  <remove-proxy no-caps dense flat two-step label="Rerun Simulation"
                                remove-label="Rerun Simulation? It is resource intensive, so please confirm"
                                @remove="runCompare" icon="mdi-refresh" color="primary"></remove-proxy>
                </q-chip>
                <div>|</div>
                <q-chip dense v-if="gps.lastSim" color="transparent" clickable @click="page = 'share'">
                  <span class="q-mr-sm tw-five">Share</span>
                  <q-icon name="mdi-share" color="secondary"></q-icon>
                </q-chip>
                <div>|</div>
                <q-chip dense color="transparent" clickable @click="getInfo">
                  <span class="q-mr-sm tw-five">How it works</span>
                  <q-icon name="mdi-information" color="primary"></q-icon>
                </q-chip>
              </div>
            </div>
            <q-separator class="q-my-sm"></q-separator>
            <!--      <template v-if="!gps.simStats?.count">-->
            <div class="_fw q-py-sm font-1r">
              <q-checkbox @update:model-value="showInactive = $event" :model-value="prefix === 'inactive_'" label="Include All Employees (non participating)"></q-checkbox>
              <div class="_fw q-py-sm text-accent" v-if="!(gps.simStats || {})[`${prefix}count`]">⚠️ There was a problem totaling your results</div>
            </div>
            <!--      </template>-->
            <template v-if="gps.lastSim">

              <div class="_fw q-py-sm">
                <q-tabs content-class="tw-five text-ir-deep" active-class="tw-six text-p5" align="left" v-model="tab"
                        no-caps indicator-color="primary">
                  <q-tab name="group">
                    <span class="text-xs">Result Summary</span>
                  </q-tab>
                  <q-tab name="tax">
                    <span class="text-xs">Tax Comparison</span>
                  </q-tab>
                  <q-tab name="individual">
                    <span class="text-xs">Individual Results</span>
                  </q-tab>
                </q-tabs>
              </div>
              <div style="padding: 0 4px">
                <div class="__res">
                  <q-tab-panels class="_panel" v-model="tab" animated>
                    <q-tab-panel class="_panel" name="group">
                      <group-totals :prefix="prefix" :gps="gps" :use-ptc="usePtc"></group-totals>
                    </q-tab-panel>
                    <q-tab-panel class="_panel" name="tax">
                      <tax-comparison :prefix="prefix" :gps="gps" :use-ptc="usePtc"></tax-comparison>
                    </q-tab-panel>
                    <q-tab-panel class="_panel" name="individual">
                      <individual-totals :prefix="prefix" :gps="gps" :use-ptc="usePtc"></individual-totals>
                    </q-tab-panel>
                  </q-tab-panels>
                </div>
              </div>

              <div class="row justify-center relative-position">
                <div id="SimExplainer" class="_cent pd10 relative-position z2">
                  <sim-explainer></sim-explainer>
                </div>
              </div>


            </template>

          </template>
        </template>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="share">
        <div class="row q-py-sm">
          <q-chip color="ir-bg2" clickable @click="page = 'results'">
            <q-icon color="primary" name="mdi-chevron-left"></q-icon>
            <span class="q-ml-sm">Back To Results</span>
          </q-chip>
        </div>
        <share-results :prefix="prefix" :use-ptc="usePtc" :gps="gps"></share-results>
      </q-tab-panel>
    </q-tab-panels>

    <turnstile-popup v-if="!isAuthenticated && !notabot" v-model:interactive="showTurnstile"
                     v-model="notabot"></turnstile-popup>


  </div>
</template>

<script setup>
  import TurnstilePopup from 'src/utils/turnstile/TurnstilePopup.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import GroupTotals from 'components/compare/group/results/tables/GroupTotals.vue';
  import TaxComparison from 'components/compare/group/results/tables/TaxComparison.vue';
  import IndividualTotals from 'components/compare/group/results/tables/IndividualTotals.vue';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';
  import SimExplainer from 'components/market/shop/utils/SimExplainer.vue';
  import ShareResults from 'components/compare/group/results/share/ShareResults.vue';

  import {computed, ref, watch} from 'vue';
  import {useGps} from 'stores/gps';
  import {$errNotify, $infoNotify, dollarString} from 'src/utils/global-methods';
  import {loginPerson} from 'stores/utils/login';
  import {useRouter} from 'vue-router';


  const { isAuthenticated } = loginPerson();

  const gpsStore = useGps();
  const router = useRouter();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true }
  })
  const notabot = ref(false);
  const showTurnstile = ref(true);

  const gps = computed(() => props.modelValue || {})

  const showInactive = ref(false);
  const loading = ref(false);
  const tab = ref('group');
  const page = ref('results');

  const prefix = computed(() => showInactive.value || !Object.keys(gps.value.coverages || {}).length ? 'inactive_' : '')

  const openStart = () => {
    const { href } = router.resolve({ name: 'compare-start', params: { gpsId: gps.value._id } })
    window.open(href, '_blank')
  }

  const notRun = computed(() => {
    return (gps.value.employees || []).filter(a => !a.sim)
  })

  const getInfo = () => {
    const el = document.getElementById('SimExplainer');
    if (el) el.scrollIntoView({ behavior: 'smooth' });
  }

  const progress = ref(0);
  const progressing = ref(false);
  const runProgress = () => {
    if (!progressing.value) return;
    progress.value += 1;
    if (progress.value < 100) setTimeout(() => {
      runProgress();
    }, 1000 * (gps.value.employees.length / 60))
    else progress.value = 0;
  }
  const stopProgress = () => {
    progressing.value = false;
    progress.value = 0;
  }

  const ready = computed(() => {
    if (!gps.value.employees?.length) return { ready: false, message: 'Add employees', noPremium: [], noDed: [] }
    let coverageMessage = '';
    const noDed = [];
    let ready = true;
    for (const c in gps.value.coverages || {}) {
      const { deductible } = gps.value.coverages[c];
      if (!Object.keys(deductible || {}).length) noDed.push(gps.value.coverages[c])
    }
    if (noDed.length) {
      coverageMessage += ` The following coverages have no deductible: ${noDed.map(a => a.name).join(', ')}.`
      if (noDed.length === Object.keys(gps.value.coverages || {}).length) {
        coverageMessage += ' Add deductible data to continue.'
        ready = false;
      } else {
        coverageMessage += ' If you do not add a deductible the plan will be excluded from the simulation.'
      }
    }
    let premiumMessage = '';
    const noPremium = [];
    for (const c in gps.value.coverages || {}) {
      const { premium } = gps.value.coverages[c];
      if (!premium) noPremium.push(gps.value.coverages[c])
    }
    if (noPremium.length) {
      premiumMessage += ` The following coverages have no premium: ${noPremium.map(a => a.name).join(', ')}.`
      if (noPremium.length === Object.keys(gps.value.coverages || {}).length) {
        premiumMessage += ' Add premium data to continue.'
        ready = false;
      } else {
        premiumMessage += ' If you do not add a premium the plan will be excluded from the simulation.'
      }
    }
    return {
      premiumMessage,
      noPremium,
      coverageMessage,
      noDed,
      ready
    };
  })

  const simProgress = ref()

  const runCompare = async () => {
    if (!notabot.value && !isAuthenticated.value) return $infoNotify('Please verify you are human')
    loading.value = true;
    progressing.value = true;
    runProgress();
    const res = await gpsStore.get(props.modelValue._id, {
      runJoin: {
        cost_sim: { exclude: [...ready.value.noDed.map(a => a.id), ...ready.value.noPremium.map(a => a.id)] }
      }
    })
        .catch(err => {
          console.error(`Error running compare: ${err.message}`)
          $errNotify(`Error running simulations - try again. ${err.message}`)
        })
    stopProgress()
    loading.value = false;
    if (res._id) {
      if (res.simProgress) {
        simProgress.value = res.simProgress;
        startCountdown()
      } else emit('update:model-value', res)
    }
  }

  const simCountdown = ref(0);
  const startCountdown = () => {
    if (!simCountdown.value) simCountdown.value = 60;
    setTimeout(() => {
      simCountdown.value--;
      if (simCountdown.value > 0) startCountdown();
      else runCompare()
    }, 1000)
  }


  const recalc = async () => {
    if (!notabot.value && !isAuthenticated.value) return $infoNotify('Please verify you are human')
    loading.value = true;
    const res = await gpsStore.patch(props.modelValue._id, { updatedAt: new Date() }, {
      runJoin: { re_total_sims: true }
    })
        .catch(err => console.error(`Error running totals: ${err.message}`))
    loading.value = false;
    console.log('got res', res);
  }

  const usePtc = ref(true);

  watch(gps, (nv) => {
    if (nv && nv.ale) {
      usePtc.value = false;
      if (nv.simProgress) {
        simProgress.value = nv.simProgress
        runCompare()
      }
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

  .__bar {
    width: 300px;
    max-width: 90vw;
    height: 10px;
    border-radius: 5px;
    background: var(--q-p1);
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .__fill {
      height: 100%;
      background: var(--q-p5);
      transition: all .2s;
      width: 100%;
    }
  }

  .__res {
    width: 100%;
    padding: 30px 1vw;
    border-radius: 12px;
    background: white;
    box-shadow: 0 2px 8px var(--ir-light);
  }
</style>
