import {AnyObj} from 'src/utils/types.js';
import {dollarString} from 'src/utils/global-methods.js';
import {_get} from 'symbol-syntax-utils';
import {computed, ComputedRef, Ref} from 'vue';
import {getStateCode} from 'src/components/common/geo/data/states';
import {costLimits} from 'components/plans/utils/index';

declare type RateKey =
    '1'
    | '2'
    | '3'
    | '4'
    | '5'
    | '6'
    | '7'
    | '8'
    | '9'
    | '10'
    | '11'
    | '12'
    | '13'
    | '14'
    | '15'
    | number | 'single' | 'plus_child' | 'plus_child__2' | 'plus_child__3' | 'family'
declare type Rate = {
    [key: string | number]: number
}
declare type FixedRates = {
    self: Rate,
    plus_spouse: Rate,
    plus_child: Rate,
    family: Rate
}
declare type Premium = {
    flatPremium?: number,
    rateByAge?: { [key: string]: number },
    smokerFactor?: number,
    multiDiscount?: Rate,
    weights?: Rate,
    fixedRates?: FixedRates
}
declare type Coverage = { premium: Premium, deductible: Rate, moop: Rate } & AnyObj

declare type PremiumOptions = {
    smoker?: Array<boolean>, //array mirroring ages with true at any smoker index
    display?: boolean,
    ages?: Array<number>
    enrollment?: any,
    rate?: any
    log?: boolean
}

type PremiumObjOpts = {
    coverage: any,
    enrollment?: any,
    rate?: any
}
export const getPremiumObj = ({coverage, enrollment, rate}: PremiumObjOpts) => {
    const def = {}
    let noLocal = true;
    if(!!enrollment && !!rate){
        const stateMatch = rate?.state !== getStateCode(enrollment?.address?.region);
        noLocal = !stateMatch
    }
    if (noLocal) {
        return coverage.premium || def;
    } else {
        const zip = (coverage.address?.postal || '').slice(0, 5);
        const stateDefault = rate.premium?.baseDefault;
        if (!zip && stateDefault) return rate.premium;
        const zipMatches = (rate.areas || []).filter((a: any) => (a.zips || []).includes(zip))
        if (zipMatches?.length) return zipMatches[0].premium || stateDefault ? rate.premium : def;
        else if (stateDefault) return rate.premium;
        return def
    }
};

export const getFixedRatePremium = ({fixedRates, key, oldest_age}: {
    fixedRates: FixedRates,
    key: string,
    oldest_age: number
}): number => {
    // console.log('get fixed rate premium', key, oldest_age, _get(fixedRates, [oldest_age || 65, key]))
    return _get(fixedRates, [oldest_age || 65, key]) || _get(fixedRates, [oldest_age || 65, key.split('__')[0]]) || 0
}


export const getDeductible = (coverage: Coverage, key: 'single' | 'family', {display, empty }: Partial<PremiumOptions>&any) => {
    if (!coverage?.deductible) return display ? empty || '' : 0
    if (!coverage.deductible && coverage?.deductible[key] !== 0) return display ? empty || '' : 0;
    const num = coverage.deductible[key] || coverage.deductible['family'] || 0
    return display ? dollarString(num, '$', 0) : num;
}

export const getmoop = (coverage: Coverage, key: 'single' | 'family', {display, empty}: Partial<PremiumOptions>&any) => {
    if (!coverage?.moop) return display ? empty || '' : 0
    if (!coverage.moop && coverage.moop[key || 'family'] !== 0) return display ? empty || '' : 0;
    const num = coverage.moop[key] || coverage.moop['family']
    return display ? dollarString(num, '$', 0) : num;
}

type Enrolled = Array<{ age:number, relation:string }>
/** take an enrollment and return the proper age banded rate key based on the enrollment in a coverage */
export const getFixedRateKey = ({enrolled, def_age, def_key}: {
    def_age?: number,
    def_key?: string,
    enrolled: Enrolled
}): {
    key: string,
    oldest_age: number
} => {
    const count = enrolled?.length;
    if(!count) return {key: def_key || 'single', oldest_age: def_age || 64}
    let key = def_key || 'single';
    let oldest_age = (enrolled || []).sort((a, b) => b.age - a.age)[0]?.age ||def_age||64;
    let spouse;
    let self;
    //loop through enrollees to determine the correct rate key
    if (count > 1) {
        for (let i = 0; i < count; i++) {
            const ee = enrolled[i];

            if (ee) {
                if (ee.relation === 'spouse') {
                    spouse = true;
                    if (count === 2) {
                        key = 'plus_spouse'
                        break;
                    } else {
                        key = 'family';
                        break;
                    }
                } else if (ee.relation !== 'self') {
                    if (!spouse && self) key = `plus_child__${Math.max(count, 3)}`
                    else if (!self) key = `plus_child__${Math.max(count, 3)}`
                    else key = 'family'
                } else {
                    self = true;
                    if (spouse) {
                        if (count === 2) {
                            key = 'plus_spouse'
                            break;
                        } else {
                            key = 'family';
                            break;
                        }
                    } else {
                        if (count === 2) {
                            key = 'plus_child';
                        } else key = `plus_child__${Math.max(count, 3)}`
                    }
                }
            }
        }
    } else oldest_age = enrolled[0]?.age || def_age || 64
    return {key, oldest_age}
}

/**
 * @param coverage
 * @param enrollment
 * @param rate
 * take enrolled and calculate premium accordingly
 */
type CoverageRateOpts = {
    coverage: any,
    enrollment?: any,
    rate?: any,
    def_age?: number,
    def_key?: string,
    enrolled?: Enrolled
}

export const simRelations = (arr:Array<any>):Array<any> => {
    arr.sort((a, b) => b.age - a.age)
    if(arr[1]){
        const diff = Math.abs(arr[1].age - arr[0].age);
        if(arr[0].relation === 'self') arr[1].relation = diff < 16 ? 'spouse' : 'child'
        else if(arr[1]?.relation === 'self') {
            arr[0].relation = diff < 16 ? 'spouse' : 'child';
        }
        else {
            arr[0].relation = 'self'
            arr[1].relation = diff < 16 ? 'spouse' : 'child'
        }
    } else if(!arr[0].relation) arr[0].relation = 'self'
    for(let i = 2; i < arr.length; i++) {
        if(!arr[i].relation) arr[i].relation = 'child'
    }
    return arr;
}

export const getEnrolled = ({enrolled, enrollment, coverage}:any) => {
    let enRolled:any = enrolled || [];
    if(!enRolled.length){
        const arr:Enrolled = []
        const participants = (_get(enrollment, `coverages.${coverage?._id}.participants`) || Object.keys(enrollment?.enrolled || {}) || []) as Array<string>
        const ages = [];
        for(let i = 0; i < participants.length; i++){
            const { age, relation } = (enrollment.enrolled || {})[participants[i]] || {};
            if(age || age === 0) {
                ages.push(i);
                arr.push({ age, relation})
            }
        }

        if(arr.length) enRolled = arr;
    }
    return enRolled
}
export const getCoverageRate = ({coverage, enrollment, rate, def_age, def_key, enrolled}: CoverageRateOpts): number => {
    if(coverage.acaPlan) return coverage.premium;
    let enRolled = getEnrolled({enrolled, enrollment, coverage})
    if(enRolled.length && enRolled.filter(a => !a.relation).length) enRolled = simRelations(enRolled);
    const l = enRolled.length;
    if (l || def_key) {
        let oldest;
        enRolled = (enRolled || []).sort((a, b) => b.age - a.age).map((a, i) => {
            if(a.relation) return a;
            if(i === 0) {
                oldest = a.age;
                a.relation = 'self'
            } else if(i === 1){
               if(a.age > 26) {
                   a.relation = 'spouse'
               } else if(a.age >= 18){
                   if(oldest > a.age + 20) a.relation = 'child'
                   else a.relation = 'spouse'
               }
            }
            return a;
        })
        const premium: any = getPremiumObj({coverage, enrollment, rate}) || {};
        const frKey = getFixedRateKey({
                enrolled:enRolled,
                def_age,
                def_key
            }
        )

        if (premium?.flatPremium && premium.flatPremium[frKey.key] && (premium.rateType === 'flatPremium' || !premium.rateType)) return premium.flatPremium[frKey.key];
        if (premium.fixedRates && (premium.rateType === 'fixedRates' || !premium.rateType)) return getFixedRatePremium({
            fixedRates: premium.fixedRates, ...frKey
        })

        const enrByKey = {'single': [{}], 'plus_spouse': [{},{}], 'plus_child': [{}, {age: 10}], 'plus_child__2': [{},{age:10},{age:10}], 'plus_child__3': [{},{age:10},{age:10},{age:10}]}
        if(!enRolled?.length && def_key) enRolled = enrByKey[def_key]
        let baseRate = 0;
        const {rateByAge = {0: 0}} = premium;
        for (const ee of enRolled || []) {
            baseRate += (rateByAge[ee.age || def_age] || 0)
        }

        if (premium.multiDiscount) {
            let discount = 1;
            const max = enRolled.length
            for(const k in premium.multiDiscount) {
                if(Number(k) <= max){
                    if((premium.multiDiscount[k]) < discount) discount = Math.max(premium.multiDiscount[k], .25);
                }
            }
            baseRate = baseRate * discount;
        }
        return baseRate

    } else return 0;
}

export const getPremium = (coverage: Coverage, key: RateKey, {
    ages = [40],
    smoker,
    display,
    enrollment,
    rate,
    log
}: PremiumOptions) => {
    if(log) console.log('getting premium', key, smoker, ages, coverage)
    if (!coverage) return display ? '-' : 0;
    const enrolled:any = ages ? ages.map(a => {
        return {
            age: a
        }
    }) : undefined;
    const r = getCoverageRate({ coverage, def_key: key as any, enrollment, rate, enrolled, def_age: (ages || [40])[0]||40})
    console.log('got rate',coverage.name, r, enrolled)
    return display ? r ? dollarString(r, '$', 0) : '-' : r;
}

export const coverageEnrolled = (coverage: any, enrollment: any) => {
    return (enrollment?.coverages || {[coverage?._id as string]: {participants: []}} as any)[coverage?._id as string]?.participants;
}
export const coverageCost = (cvg: Ref<AnyObj> | ComputedRef<AnyObj>, enrollment: Ref<AnyObj> | ComputedRef<AnyObj>) => {

    const enrolled = computed(() => {
        return coverageEnrolled(cvg.value, enrollment.value)
    })

    const totalRate = computed(() => {
        return getCoverageRate({coverage: cvg.value, enrollment: enrollment.value})
    })
    return {
        totalRate,
        enrolled
    }
}

type CoinsOptions = {
    policy: any
}
type OopRes = {
    coinsurance: number,
    deductible: { single: number, family: number, type: 'event'|'annual' },
    moop: { single: number, family: number }
}
export const getPrivateOop = ({policy}: CoinsOptions): OopRes => {
    const {coinsurance, deductible} = policy || {};
    let moop = policy.moop;
    if (!moop) {
        if (policy.type === 'mm') moop = costLimits.moop
        else moop = {family: deductible.family, single: deductible.single}
    }
    const { amount } = coinsurance || {};
    return {coinsurance: amount || amount === 0 ? amount : 1, moop, deductible: { ...costLimits.moop, ...deductible}}
    // const dedSpend = Math.min(ded, spend);
    // const coins = (spend - dedSpend) * (coinsurance || 0)
    // const all = dedSpend + coins
    // return Math.min(all, max || all)
}
export const getAcaOop = ({policy}: CoinsOptions): OopRes => {
    const moop = policy.moops || costLimits.moop
    const deductible = !policy.deductible ? costLimits.deductible : {
        family: policy.deductible.family || policy.deductible.medical?.family?.in_network || policy.deductible.medical?.single?.in_network || costLimits.deductible.family,
        single: policy.deductible.single || policy.deductible.medical?.single?.in_network || costLimits.deductible.single,
        type: policy.deductible.type || 'annual'
    }
    if (deductible.single && !deductible.family) deductible.family = deductible.single;
    else if (deductible.family && !deductible.single) deductible.single = deductible.family;
    // const ded = oopType === 'family' ? deds.filter(a => a.family)[0]?.amount : deds.filter(a => !a.family && a.type.includes('Medical'))[0]?.amount);
    let coinsurance = 0;
    // let copays = 0;
    let insAcc = 0;
    // let payAcc = 0;
    const bens: any = (policy.benefits || []);
    for (let i = 0; i < bens.length; i++) {
        const costs = bens[i].cost_sharings || []
        for (let cs = 0; cs < costs.length; cs++) {
            const cost_share = costs[cs];
            if (cost_share.network_tier === 'In-Network') {
                if (cost_share.coinsurance_rate && Number(cost_share.coinsurance_rate) <= 1) {
                    coinsurance += Number(cost_share.coinsurance_rate);
                    insAcc++
                }
                // } else if(cost_share.copay_amount) {
                // payAcc++;
                // copays += cost_share.copay_amount
                // }
            }
        }
    }
    if (bens.length) {
        coinsurance = coinsurance / (insAcc || 1)
        // copays = (copays / (payAcc || 1)) * 3 //assume 3 annual visits
    } else {
        coinsurance = .3
    }
    return {coinsurance: coinsurance || coinsurance === 0 ? coinsurance : 1, moop, deductible}
}
