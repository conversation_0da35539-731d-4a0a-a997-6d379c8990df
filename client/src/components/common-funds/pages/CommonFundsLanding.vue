<template>
  <q-page>
    <div class="_fw row justify-center">
      <div class="_cent pw1 pd12">

        <div class="row">
          <div class="col-12 col-md-8 pw1 q-py-md">
            <div class="text-sm">A revolutionary approach to paying for healthcare</div>
            <div class="text-xxl tw-five alt-font">Pool funds - not bills</div>
            <div class="text-xs">Your health insurance premiums pay your medical bills + other people's bills + shareholders. You may like the idea of people paying your bills too - but odds are (literally), you want access to shared funds, not shared bills.
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-12 col-md-4 pw1 q-py-md" v-for="(h, i) in highlights" :key="`h-${i}`">
            <div class="row justify-center">
              <div :class="`__hgh ${hov === i ? '__on' : ''}`" @pointerenter="setHov(i)" @pointerleave="hovOff(i)">
                <div>
                  <q-img class="h50 w50" :src="h.icon" fit="contain"></q-img>
                </div>
                <div>
                  <div>{{ h.label }}</div>
                  <div v-html="h.caption"></div>
                </div>

              </div>
            </div>
          </div>
        </div>


      </div>
    </div>

    <div class="row justify-center">
      <div class="_cent pd5 pw1">
        <funds-section></funds-section>
      </div>
    </div>

    <div class="row justify-center">
      <div class="_cent pd4 pw1">
        <div class="row">
          <div class="col-12 col-md-6 pw2 q-py-lg">
            <div class="font-1-1-4r tw-six alt-font text-primary">The real question we're answering differently:</div>
            <div class="font-1-1-2r">You want to put your money in a pot - with your co-workers or people on your state
              exchange - and share the total bill evenly? Everyone is approved, no underwriting, no dollar limits. You
              think this will turn out as affordable, efficient, accessible care?
            </div>
            <div class="font-1r text-ir-mid tw-five q-pa-xs">(Or more like a cruise ship's grocery bill?)</div>

            <q-separator v-if="$q.screen.gt.sm" class="q-my-sm"></q-separator>

            <div :class="`__gov ${govAnswer ? '' : '__off'}`">
              "It will work because it will be administered by federal and state governments working with the largest
              insurance companies. Also, we'll subsidize the premiums with tax credits and issue tax penalties for
              anyone who doesn't participate."
              <div class="text-xs text-italic text-right text-ir-deep">- your lawmakers & the insurance lobby</div>
            </div>


            <div class="q-pt-sm row">
              <q-chip dense class="tw-six font-1r" clickable @click="govAnswer = !govAnswer" color="transparent"
                      text-color="ir-text" :label="`${govAnswer ? 'Hide' : 'Show'} the answer the ACA gave`"
                      :icon-right="`mdi-menu-${govAnswer ? 'up' : 'down'}`"></q-chip>
            </div>
          </div>
          <div class="col-12 col-md-6 pw2 q-py-lg __bl">
            <div class="font-1-1-4r tw-six alt-font text-primary">Our answers:</div>
            <div class="__answers">
              <div v-for="(item, i) in answers" :key="`a-${i}`">
                <div>{{ item.label }}</div>
                <div>{{ item.text }}</div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
    <div class="row justify-center">
      <div class="_cent pw1 pd8">
        <against-insurance></against-insurance>
      </div>
    </div>

    <div class="row justify-center">
      <div class="w1000 mw100 pw2 pd8">
        <div class="text-sm">The bottom line:</div>
        <div class="text-lg tw-five">Insurance is for large, unexpected, infrequent events.</div>
        <div class="text-sm">Using it for everything doesn't make sense and hasn't worked out well (for you at least - insurance profits are all-time).
        </div>
      </div>
    </div>


  </q-page>
</template>

<script setup>
  import AgainstInsurance from 'components/common-funds/pages/sections/AgainstInsurance.vue';
  import FundsSection from 'components/common-funds/pages/sections/funds/FundsSection.vue';

  import commonfunds from 'src/assets/programs/pyramid.svg';
  import jubilee from 'src/assets/programs/jubilee.svg';

  import direct_care from 'src/assets/programs/panel.svg';

  import {ref} from 'vue';
  import {useEnvStore} from 'stores/env';
  import {storeToRefs} from 'pinia';

  const envStore = useEnvStore();
  const { mobile } = storeToRefs(envStore);

  const govAnswer = ref(false);

  const hov = ref(-1);
  const setHov = (i) => {
    if (hov.value === i) hov.value = -1;
    else hov.value = i;
  }
  const hovOff = (i) => {
    if (mobile.value) return;
    if (hov.value === i) hov.value = -1;
  }

  const highlights = [
    {
      icon: commonfunds,
      label: 'Build up credits',
      caption: 'When things are going your way, your contributions build up credits. Those are in your name. No use-it-or-lose-it & cash out anytime'
    },
    {
      icon: jubilee,
      label: 'Community for timing risk',
      caption: 'The hardest part about medical bills is how they hit all at once. We provide funding to manage the risk of poor timing + bill forgiveness by majority vote.'
    },
    {
      icon: direct_care,
      label: 'Better cost, better access',
      caption: 'The real key is to <i>actually</i> improve the cost, quality and accessibility of care. We do that by organizing demand and negotiating directly with doctors.'
    }
  ]
  const answers = [
    {
      label: 'It\'s better when you keep what you don\'t use',
      text: 'If the premiums you were paying stayed in your pocket when you didn\'t consume care, most of the time you would come out way ahead.'
    },
    {
      label: 'Get access to funds when you need them',
      text: 'Insurance is for large, unexpected, infrequent events. Medical bills are not. They are routine, expected, and frequent. Managing the timing risk of those bills is the only real need for insurance.'
    },
    {
      label: 'Insurance is for large, unexpected, infrequent events.',
      text: 'Insurance is for large, unexpected, infrequent events. Medical bills are not. They are routine, expected, and frequent. Managing the timing risk of those bills is the only real need for insurance.'
    }
  ]


  // onMounted(() => {
  //   setTimeout(async () => {
  //     const st = SessionStorage.getItem('state');
  //     if (st) state.value = st;
  //     else state.value = region.value;
  //   }, 1000)
  // })
</script>

<style lang="scss" scoped>
  .__box {
    padding: max(20px, 1.5vw) max(15px, 1.5vw);
    border-radius: 20px;
    background: var(--ir-bg2);
    width: 100%;
    height: 100%;
  }


  .__risk {
    padding: 15px;
    border-radius: 10px;
    //background: var(--ir-bg);
    margin-top: 10px;
  }

  .__pt {
    background: var(--ir-bg2);
    padding: 40px max(10px, 1vw);
    border-radius: 20px;
  }



  .__gov {
    padding: 15px 0;
    transition: all .3s;
    max-height: 1000px;
    overflow: hidden;
    font-size: 1.25rem;
    color: var(--q-primary);
    font-weight: 600;
  }

  .__off {
    max-height: 0;
    padding: 0;
  }

  .__hgh {
    display: grid;
    grid-template-columns: 80px 1fr;
    align-items: center;

    > div {
      &:first-child {
        height: 80px;
        width: 80px;
        border-radius: 8px;
        background: var(--ir-light);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all .5s;

        div {
          filter: brightness(0);
          transition: all .5s;

        }
      }

      &:nth-child(2) {
        padding-left: 15px;

        > div {
          &:first-child {
            font-size: 1.4rem;
            font-weight: 400;
            font-family: var(--alt-font);
          }

          &:nth-child(2) {
            font-size: 1.1rem;
            overflow: hidden;
            max-height: 0;
            opacity: 0;
            transition: all .5s;
          }
        }
      }
    }
  }

  .__on {
    align-items: start;

    > div {
      &:first-child {
        background: transparent;

        div {
          filter: none;
        }
      }

      &:nth-child(2) {
        > div {
          &:first-child {
            color: var(--q-p5);
          }

          &:nth-child(2) {
            max-height: 200px;
            opacity: 1;

          }
        }
      }
    }
  }

  .__answers {
    > div {
      padding: 10px 0;
      >div {
        &:first-child {
          font-size: 1.5rem;
          font-weight: 500;
        }

        &:nth-child(2) {
          font-size: 1rem;
          font-weight: 400;
        }
      }
    }
  }

  .__bl {
    border-left: solid 1px var(--ir-mid);
  }

  @media screen and (max-width: 1023px) {
    .__bl {
      border-left: none;
      border-top: solid 1px var(--ir-mid);
    }
  }
</style>
