<template>
  <div class="_fw">
    <div class="q-pa-md font-1-1-4r tw-six">FAQs</div>
    <div class="row">
      <div class="col-12 col-md-6 pw1">
        <template v-for="(f, i) in faqs.slice(0,4)" :key="`faq-${i}`">
          <div class="__faq" @click="faqOn=i">
            <div>{{ f.q }}</div>
            <div :class="`${faqOn === i ? '' : '__foff'}`" v-html="f.a"></div>
          </div>
          <q-separator v-if="i < 3 || $q.screen.lt.md" class="q-my-sm"></q-separator>
        </template>
      </div>
      <div class="col-12 col-md-6 pw1">
        <template v-for="(f, i) in faqs.slice(4)" :key="`faq-2-${i}`">
          <div class="__faq" @click="faqOn=i+4">
            <div>{{ f.q }}</div>
            <div :class="`${faqOn === i+4 ? '' : '__foff'}`" v-html="f.a"></div>
          </div>
          <q-separator v-if="i < faqs.length - 5" class="q-my-sm"></q-separator>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
  const faqs = [
    {
      q: 'Why buy credits if they can just be paid back?',
      a: 'Remember, we\'re sharing funds, not bills. This is about timing risk - not removing personal responsibility. It\'s also about leverage. Leverage as in: sharing funds to ensure access to care when you need it. Also, as in: when you bring a lot of friends to the negotiating table, you get better deals - a <i>lot</i> better.'
    },
    {
      q: 'How do I know how much advance credit I can get?',
      a: 'It depends on your balance, history, and how the fund is doing for the year. When we are ahead of pace, more credit is available to you. When there are more claims, there are less free funds.'
    },
    {
      q: 'Is it guaranteed my bills will be paid?',
      a: 'No. All funds are dependent on solvency of the fund. However, we project with room to breathe easy - and negotiate bills like we can\'t breathe at all.'
    },
    {
      q: 'Is it guaranteed my credit balance will be returned?',
      a: 'Yes, 80% of your credits are guaranteed to be redeemable as cash on a schedule. We leverage the fund balance for credit-worthy members which means timing sometimes matters. You are guaranteed 50% of your current year credits as cash at year-end, and 15% rolling each year. <br><br>So guaranteed return period for 80% of your unused credit is 3 years. We expect you to be able to cash-in 70-100% in any given year, but conditions can change.'
    },
    {
      q: 'Who keeps unused credits - are those profit to CommonCare?',
      a: 'No, never. CommonCare\'s monthly fee is separate and we never take a cut of funds. Not as profits, not as admin fees, 100% of funds are for member bills. The possibility of unused credits you cannot cash out is due to credit default of other members only. That is the only risk - which we try to manage carefully.'
    },
    {
      q: 'How do you guarantee proper use of these funds?',
      a: 'All use of funds is published meticulously, a third-party review is performed monthly, and a full audit of is performed annually. The guarantees about return of unused credit is a mathematical guarantee. We don\'t deviate from the scheduled limits. There is no guarantee that funds are available to pay all bills - that we have to manage.<br><br>Consider, for a moment, what it means for anyone to promise you otherwise - especially when those same organizations have systematically ratcheted up the cost of care over decades, denied claims, hamstrung providers, and degraded our care quality.'
    },
    {
      q: 'How does the Commonstrophic Fund differ from the Common Fund?',
      a: 'The credits for this fund are combined - meaning bills are shared. Also, the Common Fund has no ineligible amount - you can pay any bill from the first dollar. The Commonstrophic Fund has a $25,000 ineligible amount. Unused funds are still returned, but only your unused credits as a ratio of the total unused credits.'
    }
  ]
</script>

<style lang="scss" scoped>

  .__faq {
    padding: 10px;
    cursor: pointer;
    transition: all .4s;

    &:hover {
      transform: translate(0, -2px);
    }

    > div {
      &:first-child {
        font-size: 1.2rem;
        font-weight: 500;
      }

      &:nth-child(2) {
        font-size: 1rem;
        font-weight: 400;
        padding: 10px 0;
        max-height: 1000px;
        overflow: hidden;
        transition: all .4s;
      }
    }

  }

  .__foff {
    max-height: 0 !important;
    padding: 0 !important;
  }
</style>
