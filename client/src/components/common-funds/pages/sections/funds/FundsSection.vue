<template>
  <div class="row justify-center">
    <div class="w800 mw100 q-py-lg text-center text-lg tw-five">
      Breathtaking simplicity and transparency
      <div class="text-sm tw-four text-ir-deep">Structured by numbers - operated by humans.</div>
    </div>
  </div>
  <div class="row justify-center">
    <div class="__azf">
      <age-zip-family omit="gender,zip"></age-zip-family>
    </div>
  </div>
  <q-tab-panels class="_panel" v-model="tab" animated>
    <q-tab-panel class="_panel" name="base">
      <div class="row q-py-md">
        <div class="col-12 col-md-4 q-px-sm q-py-md _fh" v-for="(f, i) in Object.keys(funds)"
             :key="`fund-preview-${i}`">
          <div :class="`__q ${hov === i ? '' : '__off'}`" @click="tab = f" @pointerenter="setHov(i)" @pointerleave="hovOff(i)">
            <div class="z2">
              <div>{{ funds[f].label }}</div>
              <div>{{ funds[f].description }}</div>
              <q-img :src="funds[f].icon" fit="contain" class="__img"></q-img>
            </div>
            <div class="font-1-1-4r text-primary alt-font tw-six q-pt-md z2">
              {{ dollarString(funds[f].premium, '$', 0, 'TBD') }}<span class="font-1r tw-five text-ir-text">/mo</span>
            </div>
          </div>
        </div>
      </div>
    </q-tab-panel>
    <q-tab-panel class="_panel" v-for="(f, i) in Object.keys(funds)" :key="`fund-${i}`" :name="f">
      <div class="row q-pb-md">
        <q-chip color="transparent" clickable @click="tab = 'base'">
          <q-icon name="mdi-chevron-left" color="primary" class="q-mr-sm"></q-icon>
          <span>Back</span>
        </q-chip>
      </div>


      <div class="row">
        <div class="col-12 col-md-6 pw1 q-py-md">
          <div class="__q __nb">
            <div class="z2">
              <div>{{ funds[f].label }}</div>
              <div>{{ funds[f].description }}</div>
              <q-img :src="funds[f].icon" fit="contain" class="__img"></q-img>
            </div>
            <div class="font-1-1-4r text-primary alt-font tw-six q-pt-md z2">
              {{ dollarString(funds[f].premium, '$', 0, 'TBD') }}<span class="font-1r tw-five text-ir-text">/mo</span>
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 pw1 q-py-md">
          <div class="__bullet" v-for="(b, i) in funds[f].bullets" :key="`bullet-${i}`">
            <div>
              <q-img class="_fa" fit="contain" :src="funds[f].icon"></q-img>
            </div>
            <div class="text-sm tw-five">{{ b }}</div>
          </div>
        </div>
      </div>




    </q-tab-panel>
  </q-tab-panels>

  <div class="q-pt-lg">
    <div class="q-pa-md font-1-1-4r tw-six">FAQs</div>
    <div class="row">
      <div class="col-12 col-md-6 pw1">
        <template v-for="(f, i) in faqs.slice(0,4)" :key="`faq-${i}`">
        <div class="__faq" @click="faqOn=i">
          <div>{{ f.q }}</div>
          <div :class="`${faqOn === i ? '' : '__foff'}`" v-html="f.a"></div>
        </div>
        <q-separator v-if="i < 3 || $q.screen.lt.md" class="q-my-sm"></q-separator>
        </template>
      </div>
      <div class="col-12 col-md-6 pw1">
        <template v-for="(f, i) in faqs.slice(4)" :key="`faq-2-${i}`">
        <div class="__faq" @click="faqOn=i+4">
          <div>{{ f.q }}</div>
          <div :class="`${faqOn === i+4 ? '' : '__foff'}`" v-html="f.a"></div>
        </div>
          <q-separator v-if="i < faqs.length - 5" class="q-my-sm"></q-separator>
        </template>
      </div>
    </div>
  </div>

</template>

<script setup>
  import commonfunds from 'src/assets/programs/pyramid.svg';
  import jubilee from 'src/assets/programs/jubilee.svg';
  import catastrophic from 'src/assets/programs/catastrophic.svg';
  import AgeZipFamily from 'pages/landing/could-be/utils/AgeZipFamily.vue';

  import {computed, ref} from 'vue';
  import {useEnvStore} from 'stores/env';
  import {storeToRefs} from 'pinia';
  import {staticRates, cfMultiples} from 'components/common-funds/utils';
  import {dollarString} from 'src/utils/global-methods';

  const envStore = useEnvStore();
  const { household, mobile } = storeToRefs(envStore);

  const tab = ref('base');
  const faqOn = ref(false);

  const hov = ref(-1);
  const setHov = (i) => {
    if (hov.value === i) hov.value = -1;
    else hov.value = i;
  }
  const hovOff = (i) => {
    if (mobile.value) return;
    if (hov.value === i) hov.value = -1;
  }

  const funds = computed(() => {
    const premiums = {
      common: 0,
      catastrophic: 0
    }

    for (const p of household.value.people) {
      premiums.common += staticRates.common * cfMultiples[p.age];
      premiums.catastrophic += staticRates.catastrophic * cfMultiples[p.age];
    }

    return {
      'common': {
        icon: commonfunds,
        label: 'Common Fund',
        description: 'Always your funds - you can use your fund to pay bills and get advance credit for bills that exceed your credit balance. Unused credits can be cashed in each year - so you don\'t lose by not using them.',
        premium: premiums.common,
        bullets: ['Your funds buy credits - which are always yours, you never lose them', 'Credits are used just like dollars to pay medical bills', 'Our team negotiates your bills down - the easy way or the hard way', 'Thousands of preferred providers take our credits already', 'Unused credits can be cashed out at the end of each plan year', 'Bills that exceed your credit are eligible for up to 20x your credit balance advance credit']
      },
      'catastrophic': {
        icon: catastrophic,
        label: 'Commonstrophic Fund',
        description: 'Sharing risk works well for large, infrequent, unexpected events. This fund is for that. Large unexpected bills. Unused funds are returned pro-rata at year-end.',
        premium: premiums.catastrophic,
        bullets: ['The first $25,000 of any medical event is ineligible', 'We still use credits, but credits are combined toward all bills', 'Unused funds are returned pro-rated at year-end', 'Pre-existing conditions are subject to approval before joining', 'Physician-led bill negotiation & settlement']
      },
      'jubilee': {
        icon: jubilee,
        label: 'Jubilee Fund',
        description: 'Top-down "healthcare-for-all" is such a complex idea - you should be leary of anyone who sells it. Voluntary is beautiful. Each year, you can forego credits to pay bills you feel are deserving.',
        bullets: ['A fund for truly unfortunate circumstances', 'Allows scrupulous spending to relieve real need', 'Facilitated by a 501(c)(3) non-profit for tax purposes', 'There is abundance to share when transparency is prioritized', 'You choose which bills you want to contribute to and how much'],
        premium: 'TBD'
      }
    }
  })

  const faqs = [
    {
      q: 'Why buy credits if they can just be paid back?',
      a: 'Remember, we\'re sharing funds, not bills. This is about timing risk - not removing personal responsibility. It\'s also about leverage. Leverage as in: sharing funds to ensure access to care when you need it. Also, as in: when you bring a lot of friends to the negotiating table, you get better deals - a <i>lot</i> better.'
    },
    {
      q: 'How do I know how much advance credit I can get?',
      a: 'It depends on your balance, history, and how the fund is doing for the year. When we are ahead of pace, more credit is available to you. When there are more claims, there are less free funds.'
    },
    {
      q: 'Is it guaranteed my bills will be paid?',
      a: 'No. All funds are dependent on solvency of the fund. However, we project with room to breathe easy - and negotiate bills like we can\'t breathe at all.'
    },
    {
      q: 'Is it guaranteed my credit balance will be returned?',
      a: 'Yes, 80% of your credits are guaranteed to be redeemable as cash on a schedule. We leverage the fund balance for credit-worthy members which means timing sometimes matters. You are guaranteed 50% of your current year credits as cash at year-end, and 15% rolling each year. <br><br>So guaranteed return period for 80% of your unused credit is 3 years. We expect you to be able to cash-in 70-100% in any given year, but conditions can change.'
    },
    {
      q: 'Who keeps unused credits - are those profit to CommonCare?',
      a: 'No, never. CommonCare\'s monthly fee is separate and we never take a cut of funds. Not as profits, not as admin fees, 100% of funds are for member bills. The possibility of unused credits you cannot cash out is due to credit default of other members only. That is the only risk - which we try to manage carefully.'
    },
    {
      q: 'How do you guarantee proper use of these funds?',
      a: 'All use of funds is published meticulously, a third-party review is performed monthly, and a full audit of is performed annually. The guarantees about return of unused credit is a mathematical guarantee. We don\'t deviate from the scheduled limits. There is no guarantee that funds are available to pay all bills - that we have to manage.<br><br>Consider, for a moment, what it means for anyone to promise you otherwise - especially when those same organizations have systematically ratcheted up the cost of care over decades, denied claims, hamstrung providers, and degraded our care quality.'
    },
    {
      q: 'How does the Commonstrophic Fund differ from the Common Fund?',
      a: 'The credits for this fund are combined - meaning bills are shared. Also, the Common Fund has no ineligible amount - you can pay any bill from the first dollar. The Commonstrophic Fund has a $25,000 ineligible amount. Unused funds are still returned, but only your unused credits as a ratio of the total unused credits.'
    }
  ]

</script>

<style lang="scss" scoped>
  .__azf {
    padding: 15px;
    //border-radius: 10px;
    background: white;
    width: 100%;
    max-width: 300px;
    border-top: solid 3px var(--ir-light);
    //border-bottom: solid 3px var(--ir-light);
  }

  .__q {
    padding: 25px;
    border-radius: 12px;
    border: solid 3px var(--q-p2);
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: 1fr auto;
    transition: all .4s;
    box-shadow: 0 2px 5px var(--ir-light);
    cursor: pointer;
    overflow: hidden;
    position: relative;

    .__img {
      position: absolute;
      z-index: 0;
      transition: all .4s;
      top: 10px;
      right: 10px;
      width: 110px;
      height: 110px;
      opacity: .2;
    }

    > div {
      &:first-child {
        > div {
          &:first-child {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--q-primary);
            font-family: var(--alt-font);
            transition: all .4s;
          }

          &:nth-child(2) {
            font-size: 1.1rem;
            font-weight: 400;
            color: var(--ir-text);
          }
        }
      }

    }
  }

  .__off {
    box-shadow: none;
    border: solid 3px var(--ir-light);

    .__img {
      top: -10px;
      right: -10px;
      width: 100px;
      height: 100px;
      opacity: .6;
    }

    > div {
      &:first-child {
        > div {
          &:first-child {
            color: var(--ir-mid) !important;
          }
        }
      }
    }
  }
  .__nb {
    border:none;
    border-top: solid 3px var(--ir-light);
    border-bottom: solid 3px var(--ir-light);
    border-radius: 0;
    box-shadow: none;
  }
  .__bullet {
    display: grid;
    padding: 10px;
    grid-template-columns: auto 1fr;
    align-items: center;

    > div {
      &:first-child {
        overflow: hidden;
        height: 25px;
        width: 25px;
      }

      &:nth-child(2) {
        padding-left: 15px;
        font-size: 1.1rem;
      }
    }
  }

  .__faq {
    padding: 10px;
    cursor: pointer;
    transition: all .4s;

    &:hover {
      transform: translate(0, -2px);
    }

    > div {
      &:first-child {
        font-size: 1.2rem;
        font-weight: 500;
      }

      &:nth-child(2) {
        font-size: 1rem;
        font-weight: 400;
        padding: 10px 0;
        max-height: 1000px;
        overflow: hidden;
        transition: all .4s;
      }
    }

  }

  .__foff {
    max-height: 0 !important;
    padding: 0 !important;
  }

</style>
