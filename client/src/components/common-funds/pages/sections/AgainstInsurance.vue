<template>
  <div class="_fw">
    <div class="row">
      <div class="_fw mw800 q-pb-lg pw2">
        <div class="text-lg tw-five">Learn why it's time for health insurance as we know it - to go the way of the
          dodo 🦤.
        </div>

      </div>
    </div>

    <q-tab-panels keep-alive class="_panel" v-model="tab" animated>
      <q-tab-panel keep-alive class="_panel" :name="-1">
        <div class="row">
          <div class="col-12 col-md-4 q-pa-sm" v-for="(section, i) in sections" :key="`head-${i}`">
            <div class="row justify-center">
              <div class="__pc" @click="tab = i">
                <div>
                  <q-icon color="primary" size="40px" :name="section.icon"></q-icon>
                </div>
                <div class="flex items-center">
                  <div>{{ section.short }}</div>
                  <q-icon class="q-ml-xs" name="mdi-arrow-right" color="primary"></q-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-tab-panel>
      <q-tab-panel keep-alive class="_panel" v-for="(section, i) in sections" :key="`panel-${i}`" :name="i">
        <div class="__section">
          <div class="_fw">
            <q-chip color="transparent" clickable @click="tab = -1">
              <q-icon name="mdi-chevron-left" color="primary"></q-icon>
              <span class="q-ml-sm">Back</span>
            </q-chip>
          </div>
          <div class="row">
            <div class="q-pb-lg q-pt-sm q-px-sm w1000 mw100">
              <div class="text-md tw-five">{{ section.title }}</div>
              <div class="text-xs q-py-sm">{{ section.caption }}</div>
            </div>
          </div>
          <component v-on="section.listeners" :is="section.component" v-bind="section.attrs"></component>

        </div>
      </q-tab-panel>
    </q-tab-panels>


  </div>
</template>

<script setup>
  import InsuranceMoney from 'components/common-funds/pages/sections/InsuranceMoney.vue';
  import PtSection from 'components/common-funds/pages/sections/PtSection.vue';
  import SpendDist from 'components/common-funds/pages/sections/SpendDist.vue';

  import {computed, ref, watch} from 'vue';
  import {storeToRefs} from 'pinia';
  import {useEnvStore} from 'stores/env';
  import {useShops} from 'stores/shops';

  const shopStore = useShops();

  const envStore = useEnvStore();

  const tab = ref(-1)

  const { household, age } = storeToRefs(envStore);
  const shop = ref({})
  const risk = ref(3);


  const watchKey = computed(() => {
    const length = household.value.people.length;
    return `${length}${age.value}`
  })


  const findTo = ref()
  const findShop = async (p = 0, r = 0, a = 0, force) => {
    if (shop.value._id && !force) return;
    const peopleSize = Math.max(household.value.people.length - 1, 0);
    const adjustments = [0, 1, -1, -2, -3, -4, -5]
    const riskAdjustments = [0, 1, -1, 2, -2]
    const res = await shopStore.find({
      query: {
        $sort: { age: 1 },
        $limit: 1,
        // 'stats.age': { $gt: 0, $lt: 100 },
        'stats.age': { $gt: age.value + a },
        'stats.people': { $size: Math.max(0, peopleSize + adjustments[p]) },
        'stats.risk': risk.value + riskAdjustments[r]
      }
    })
    if (res.data[0]) {
      shop.value = res.data[0];
    } else if (p < 6) {
      if (r < 4) await findShop(p, r + 1, a)
      else await findShop(p + 1, 0, a)
    } else if (a > -20) {
      await findShop(0, 0, a - 2)
    }
  }

  watch(watchKey, (nv, ov) => {
    if (nv !== ov) {
      clearTimeout(findTo.value);
      findTo.value = setTimeout(() => {
        findShop(0, 0, 0)
      }, 1000)
    }
  }, { immediate: true })


  const sections = computed(() => [
    {
      icon: 'mdi-cards-playing-spade',
      short: 'Don\'t make a bad bet',
      title: 'Health insurers make money on you.',
      caption: 'Going "uninsured" feels like a bad bet to most people - but is it? It\'s a statistical certainty that most people will pay more in premiums than they would ever pay in medical bills. That\'s to say nothing of what those bills would cost if insurance wasn\'t inflating the price.',
      component: InsuranceMoney,
      attrs: {
        shop: shop.value
      },
      listeners: {
        'update:shop': (v) => {
          shop.value = v
        },
        'update:risk': (v) => {
          risk.value = v;
        }
      }
    },
    {
      icon: 'mdi-chart-pie',
      short: 'Great care isn\'t expensive',
      title: 'Have a closer look at the cost of care',
      caption: 'This is a price transparency file - legally required published hospital pricing. They don\'t assume your physician negotiated or that you even called to ask for a discount. They are just the standard prices. This is how those ghastly insurance premiums get justified. It also keeps you believing that healthcare is a very expensive thing. You have better access to better care with your credit card than with your insurance card.',
      component: PtSection,
      listeners: {}
    },
    {
      icon: 'mdi-swim',
      short: 'You\'re in the wrong pool',
      title: 'Insurance hinges on underwriting',
      caption: 'Yet, for health insurance, individuals can\'t be underwritten. You are at an all-you-can-eat buffet - where they weigh the food and charge everyone an equal split regardless of how much they ate. Odds are, that\'s a bad deal for you.',
      component: SpendDist,
      listeners: {},
      attrs: { shop: shop.value }
    },
    // {
    //   short: 'We\'re not helping the needy',
    //   title: 'Nobody wants to be left out in the cold',
    //   caption: 'The whole idea of the ACA was to provide equitable coverage - and it does that to an extent. However, nobody is harmed more by the way things ended up than those in greatest need. Let\'s explore a great need.'
    // },
    // {
    //   short: 'Middlemen reduce care quality',
    //   title: 'Healthcare is a business - and that\'s good',
    //   caption: 'Or, it can be good. Look at how reimbursements predict services. Not clinical evidence, patient discussions, peer recommendations - payment. Change who decides what you\'d be willing to pay for - to you. If you have insurance - it\'s not you..'
    // },
    // {
    //   short: 'Moral hazard ahead',
    //   title: 'Healthcare is hard - bad incentives make it impossible',
    //   caption: ''
    // }
  ])
</script>

<style lang="scss" scoped>

  .__title {
    font-size: var(--text-lg);
    font-weight: 600;
    font-family: var(--alt-font);
    border-radius: 10px;
    padding: 5px 0;
  }

  .__section {
    padding: 20px max(10px, 1vw);
    //box-shadow: 0 6px 10px rgba(0,0,0,.1);
    //border-radius: 20px;
    background: white;
  }

  .__pc {
    width: 500px;
    max-width: 100%;
    display: grid;
    grid-template-columns: 80px 1fr;
    cursor: pointer;
    padding: 10px;
    border-radius: 10px;
    background: var(--ir-bg);
    transition: all .3s;
    align-items: center;
    align-content: center;
    justify-content: center;

    &:hover {
      transform: translate(0, -2px);
    }

    > div {
      &:first-child {
        height: 80px;
        width: 80px;
        border-radius: 8px;
        background: var(--ir-bg2);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &:nth-child(2) {
        font-size: 1.2rem;
        font-weight: 500;
        padding-left: 15px;
        color: var(--ir-text);
      }
    }
  }
</style>
