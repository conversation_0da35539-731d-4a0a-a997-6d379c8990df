<template>
  <div class="_fw">

    <div class="row">
      <div class="col-12 col-md-5 q-pa-sm _fh">
        <div class="__box">
          <div class="font-1-1-4r tw-five">
            <div>Household Profile</div>
            <div class="font-1r text-ir-deep tw-five">If you want results most relevant to you</div>
          </div>
          <div class="_fw q-py-md">
            <div class="w500 mw100">
              <div class="__azf">
                <age-zip-family omit="zip"></age-zip-family>
              </div>
              <div class="q-pt-sm">
                <div class="__risk">
                  <risk-profile
                      :people="household.people"
                      :age="age"
                      :model-value="risk"
                      @update:modelValue="setRisk"
                  ></risk-profile>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-7 q-pa-sm _fh">
        <div class="__box relative-position">
          <div class="row q-pb-sm" v-if="!shop?._id">
            <ai-logo opaque></ai-logo>
          </div>
          <div class="font-1r text-ir-deep tw-five w400 mw100">This is a normal actuarial schedule for medical bills
            for your household <span
                class="font-7-8r tw-six alt-font text-ir-deep">(You - age {{
                age
              }} {{
                (spouse || [])[0] ? ' + spouse' : ''
              }}{{ deps?.length ? ` + ${$possiblyPlural('dependent', deps)}` : '' }})</span>
          </div>
          <bill-chart
              :coverage-id="coverageId"
              :filterFn="billFilterFn"
              :multiplier="multiplier"
              :shop="shop"
              @total="total = $event"
              @average="average = $event"
          ></bill-chart>

        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-12 col-md-7 q-pa-sm _fh">
        <div class="__box">
          <div class="row __nums">
            <div class="col-12 col-md-6 q-py-md q-px-sm">
              <div class="__head">All Costs - <span class="text-primary">No</span> Coverage</div>
              <div class="__amt">
                <div>{{ dollarString(total, '$', 0) }}</div>
                <div>{{ dollarString(total / 50, '$', 0) }}<span>/yr</span></div>
              </div>
            </div>
            <div class="col-12 col-md-6 q-py-md q-px-sm">
              <div class="__head">All Costs - <span class="text-accent">With</span> Coverage</div>
              <div class="__amt text-accent">
                <div>{{ dollarString((average || 0) * 50, '$', 0) }}</div>
                <div>{{ dollarString((average || 0), '$', 0) }}<span>/yr</span></div>
              </div>

            </div>
          </div>

          <div class="font-1r">
            <ul>
              <li>This is 50 years of simulated medical bills using actuarial norms for your profile</li>
              <li>We compared the raw cost to what you would pay with insurance including premiums and deductibles.
              </li>
              <li>THe insurance analysis looked at hundreds of available policies for your zip code and selected the
                plan with the lowest total cost. <span class="cursor-pointer text-accent tw-six font-7-8r"
                                                       @click="cDialog = true">See Plans</span>
              </li>
              <li>These aren't cash or negotiated prices - these are just norms. We will talk about the actual cost
                of care elsewhere.
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col-12 col-md-5 q-pa-sm _fh">

        <div class="__box">
          <div class="_fa flex flex-center">
            <div class="_fw">
              <div :class="`text-${readMore ? 'md' : 'lg'} tw-five`">Insurance has sold you that a "better" policy means
                lower deductibles and
                higher premiums. <span class="text-primary">The numbers tell a
                different story.</span>
              </div>
              <div :class="`__exp ${readMore ? '' : '__off'}`">
                <div class="font-1r tw-four w400 mw100 _fw q-pt-sm">
                  Insurance companies exist to earn profits. That means you most likely pay more in premiums than
                  they
                  pay out in claims. Statistically, health insurance will most likely be a losing arrangement.

                  <div class="q-pt-sm">It's worse than that though. Under the ACA insurance companies can't profit
                    the
                    way you
                    think they would: by keeping the cost of care down. Instead profits are a regulated percentage
                    of
                    claims - meaning increasing profits requires increasing the actual cost of care.
                  </div>
                  <div class="q-pt-sm">The bottom line is: the more of your care insurance controls, the higher the
                    cost
                    of care. Insurance definitionally just can't be for everything. Time to stop using it that way.
                  </div>
                </div>
              </div>
              <div class="_fw q-pt-sm">
                <q-chip class="tw-six text-primary" dense color="transparent" clickable
                        @click="readMore = !readMore" :label="`Read ${readMore ? 'Less' : 'More'}`"
                        :icon-right="`mdi-menu-${readMore ? 'up' : 'down'}`"></q-chip>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <common-dialog v-model="cDialog" setting="smmd">
      <div class="q-py-lg pw1">
        <q-input dense filled v-model="cSearch">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>

        <div class="_fw __c" v-for="(c, i) in coverages" :key="`c-${i}`">
          <coverage-card v-if="!c.acaPlan" hide-for :model-value="c"></coverage-card>
          <policy-card :model-value="c" v-else></policy-card>

          <div class="q-pt-sm row justify-end">
            <q-chip v-if="!coverageId && i === 0 || coverageId === c._id" color="transparent" text-color="green"
                    label="Selected" icon-right="mdi-check-circle">
            </q-chip>
            <q-chip v-else clickable @click="coverageId = c._id" color="accent" text-color="white" label="Select"
                    icon-right="mdi-check-circle-outline">
            </q-chip>
          </div>
        </div>

        <div class="q-py-md row justify-center" v-if="cSlice < shop.coverages?.length">
          <q-chip clickable @click="cSlice += 10" color="ir-deep" text-color="white" label="Load More"
                  icon-right="mdi-plus"></q-chip>
        </div>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import BillChart from 'components/market/shop/cards/BillChart.vue';
  import AgeZipFamily from 'pages/landing/could-be/utils/AgeZipFamily.vue';
  import RiskProfile from 'components/market/shop/utils/RiskProfile.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import CoverageCard from 'components/coverages/cards/CoverageCard.vue';
  import PolicyCard from 'components/enrollments/ichra/cards/PolicyCard.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {computed, ref, watch} from 'vue';
  import {useEnvStore} from 'stores/env';
  import {storeToRefs} from 'pinia';
  import {$possiblyPlural} from 'src/utils/global-methods';
  import {riskMultiples, billsByAge} from 'components/market/shop/utils';
  import {dollarString} from 'symbol-syntax-utils';

  const envStore = useEnvStore();
  const { household, age, spouse, deps } = storeToRefs(envStore);

  const emit = defineEmits(['update:shop', 'update:risk']);

  const props = defineProps({
    shop: {
      default: () => {
        return {}
      }
    }
  })

  const risk = ref(3);
  const total = ref(0);
  const average = ref(0);
  const multiplier = ref(1);


  const readMore = ref(false);

  const coverageId = ref()
  const cDialog = ref(false);
  const cSlice = ref(10);
  const cSearch = ref('');
  const coverages = computed(() => {
    if (!props.shop.coverages) return [];
    const scores = props.shop.coverage_scores || {}
    const list = [];
    const covs = [...props.shop.coverages].filter(a => ['mm', 'aca'].includes(a.type)).sort((a, b) => scores[a._id]?.average - scores[b._id]?.average)
    for (let i = 0; i < covs.length; i++) {
      if (i >= cSlice.value) break;
      const c = covs[i];
      if (c.name?.toLowerCase().includes(cSearch.value.toLowerCase())) {
        c.premium = (scores[c._id]?.premium || 0) / 12;
        list.push(c);
      }
    }
    return list;
  })

  const coverageObj = computed(() => {
    const obj = {};
    for (const cov of props.shop?.coverages || []) obj[cov._id] = cov;
    return obj;
  })
  const billFilterFn = (a) => {
    const cvg = coverageObj.value[a._id]
    if (!cvg) return true;
    return ['aca', 'mm'].includes(cvg.type);
  }

  const setMultiplier = () => {
    if (!props.shop.stats) return;
    const riskM = riskMultiples[risk.value] / riskMultiples[props.shop.stats.risk];
    const count = household.value.people.length || 1;
    const shopCount = (props.shop.stats.people?.length || 0) + 1;
    const pplM = count / shopCount;
    const shopAge = (props.shop.stats.age + (props.shop.stats.people || []).reduce((acc, a) => acc + a.age, 0)) / shopCount;
    const aGe = household.value.people.reduce((acc, a) => acc + a.age, age.value) / count;
    const avgs = { shop: 0, here: 0 }
    for (const k in billsByAge) {
      if (shopAge <= Number(k)) {
        avgs.shop = billsByAge[k];
        break;
      }
    }
    for (const k in billsByAge) {
      if (aGe <= Number(k)) {
        avgs.here = billsByAge[k];
        break;
      }
    }
    const ageM = avgs.here / avgs.shop;
    multiplier.value = riskM * pplM * ageM;
  }

  watch(() => props.shop, (nv, ov) => {
    if (nv && nv.resetId !== ov?.resetId) setMultiplier();
  }, { immediate: true })

  const setRisk = (v) => {
    const run = risk.value !== v;
    risk.value = v;
    if (run) setMultiplier();
    emit('update:risk', v)
  }


</script>

<style lang="scss" scoped>
  .__box {
    padding: max(20px, 1.5vw) max(15px, 1.5vw);
    border-radius: 20px;
    background: var(--ir-bg2);
    width: 100%;
    height: 100%;
    transition: all .3s;
  }

  .__azf {
    padding: 15px;
    border-radius: 10px;
    background: white;
  }

  .__risk {
    padding: 15px;
    border-radius: 10px;
    //background: var(--ir-bg);
    margin-top: 10px;
  }

  .__head {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--ir-mid);
    text-align: center;

  }

  .__amt {
    color: var(--q-p5);

    > div {
      font-weight: 600;
      font-family: var(--alt-font);
      text-align: center;

      &:first-child {
        font-size: var(--text-lg);
      }

      &:nth-child(2) {
        font-size: var(--text-xs);

        > span {
          font-size: var(--text-xxs);
          font-weight: 500;
        }
      }
    }
  }

  li {
    padding: 3px 0;
  }

  .__c {
    border-radius: 10px;
    margin: 15px 0;
    padding: 15px;
    box-shadow: 0 2px 6px var(--ir-light);
    background: white;
  }

  .__exp {
    max-height: 1000px;
    overflow: hidden;
    transition: all .3s;
  }

  .__off {
    max-height: 0;
  }
</style>
