import {PlanGenerator} from './plan-admin';
import {commonRoutes} from 'src/router/utils/common-routes';

export const appAdminRoutes = () => [
    {
        path: '/',
        component: () => import('src/layouts/AdminLayout.vue'),
        meta: {},
        children: [
            {
                path: '/login/:action?',
                name: 'login',
                component: () => import('src/components/auth/pages/LoginPage.vue')
            },
            {
                path: '/oauth',
                name:
                    'oauth',
                component:
                    () => import('src/components/auth/pages/OauthLanding.vue')
            },
            ...[{
                path: '',
                name: 'admin',
                component: () => import('src/pages/AdminHome.vue')
            },
                {
                    path: 'abilities',
                    name: 'abilities',
                    meta: {
                        name: 'abilities'
                    },
                    component: () => import('src/components/capabilities/CapabilityAdmin.vue')
                },
                {
                    path: 'leads',
                    name: 'leads',
                    meta: {name: 'leads'},
                    component: () => import('src/components/reqs/admin/LeadsAdmin.vue')
                },
                {
                   path: 'prices',
                   meta: { name: 'prices' },
                   component: () => import('src/components/providers/bundles/admin/ConsolePricing.vue'),
                    children: [
                        {
                            path: '',
                            name: 'prices-admin',
                            meta: { name: 'prices' },
                            component: () => import('src/components/providers/bundles/admin/ConsoleBundles.vue')
                        }
                    ]
                },
                {
                    path: 'hosts',
                    name: 'hosts-admin',
                    meta: {
                        ucan: {
                            requiredCapabilities: [['hosts', '*']]
                        }
                    },
                    component: () => import('src/components/hosts/pages/HostsAdmin.vue')
                },
                {
                    path: 'flow-charts',
                    meta: {
                        name: 'flow-charts'
                    },
                    name: 'flow-charts',
                    component: () => import('src/components/utils/flow-charts/FlowPage.vue')
                },
                {
                    path: 'plans',
                    name: 'console-plan-admin',
                    component: () => import('src/components/plans/templates/TemplateAdmin.vue')
                },
                {
                    path: 'plans/:planId',
                    name: 'plan-edit',
                    component: () => import('src/components/plans/forms/PlanGenerator.vue'),
                    children: PlanGenerator('plans')
                },
                {
                    path: 'providers',
                    name: 'console-providers',
                    component: () => import('src/components/providers/admin/ConsoleProviders.vue')
                },
                {
                    path: 'providers/:providerId',
                    name: 'provider-edit',
                    component: () => import('src/components/providers/admin/ConsoleProviderEdit.vue')
                },
                {
                    path: 'coverages',
                    name: 'coverages-admin',
                    component: () => import('src/components/coverages/pages/CoveragesAdmin.vue')
                },
                {
                   path: 'health-shares',
                   name: 'health-shares-admin',
                   component: () => import('src/components/coverages/info/health-shares/HsAdmin.vue')
                },
                {
                    path: 'docs/:id?',
                    name: 'docs-admin',
                    component: () => import('src/components/plans/docs/admin/pages/DocTemplatesAdmin.vue')
                },
                {
                   path: 'state-exchanges',
                   name: 'se-plans',
                   component: () => import('src/components/market/se-plans/SeAdmin.vue')
                },
                {
                    path: 'ai',
                    name: 'ai-controls',
                    component: () => import('src/components/ai/admin/AiSettings.vue')
                },
                {
                    path: 'issues/:id?',
                    name: 'issues-admin',
                    component: () => import('src/components/issues/pages/IssuesAdmin.vue'),
                    meta: {}
                }].map((a:any) => {
                a.meta = {
                    ucan: {
                        requiredCapabilities: [['ppls', '*']]
                    },
                    ...a.meta
                }
                return a;
            })
        ]
    }
]
