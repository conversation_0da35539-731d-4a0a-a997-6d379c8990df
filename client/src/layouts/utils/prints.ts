import {useRefs} from 'src/stores/refs';
import {LocalStorage, SessionStorage} from 'symbol-auth-client';
import {useRoute} from 'vue-router';
import { loginPerson } from 'src/stores/utils/login';
import { useEnvStore } from 'src/stores/env';
import {useFingerprints} from 'src/stores/fingerprints';
import {checkOrAdd} from 'src/fingerprints';
import { useMyIp } from 'src/stores/my-ip';
import {onMounted, ref} from 'vue';
import {getStateCode, getStateName} from 'components/common/geo/data/states';
import {useJunkDrawers} from 'stores/junk-drawers';

type PrintOptions = {
    afterFn?: (...args:any) => void,
    autoRun?:boolean
}
export const visitorPrints = ({ afterFn, autoRun = true }:PrintOptions) => {
    const { login } = loginPerson()
    const envStore = useEnvStore();
    const fingerprintStore = useFingerprints();
    const myIpStore = useMyIp();
    const route = useRoute();
    const junkStore = useJunkDrawers();

    const pending = ref(false);
    const checkRef = async () => {
        setTimeout(() => {
            const refStore = useRefs();
            const id = route.query.ref_id;
            const existing = LocalStorage.getItem('ref_id');
            if (id && id !== existing && String(login.value?._id || '*') !== id) {
                LocalStorage.setItem('ref_id', id);
                const fp = LocalStorage.getItem('fpId');
                refStore.create({ ref: id, fingerprint: fp, referred: login.value?._id || undefined });
            }
        }, 1000)
    };

    const ipCallback = (fgpt:any) => {
        if(!LocalStorage.getItem('fpId')) {
            envStore.setIp(fgpt.ipInfo);
            fingerprintStore.create({...fgpt.fingerprint})
                .then((fpRes) => {
                    LocalStorage.setItem('fpId', fpRes._id);
                    return fpRes;
                })
                .catch(err => {
                    console.error('err saving fingerprint: ', err);
                });
        }
        return fgpt;
    }

    const runPrint = async () => {
        pending.value = true;

        // Get the user's real IP from external service, then get geolocation
        let ipInfo;
        try {
            // First try to get real IP from external service
            const { getUserIP } = await import('src/fingerprints/utils/getUserIPs');
            const realIp = await getUserIP();
            console.log('Got real IP from external service:', realIp);

            // Then get geolocation data for that IP
            ipInfo = await myIpStore.get(realIp);
            console.log('Got geolocation for IP:', ipInfo);
        } catch (err) {
            console.warn('Failed to get real IP, falling back to server detection:', err);
            // Fallback to server-side IP detection
            ipInfo = await myIpStore.get('1');
        }

        const res:any = await checkOrAdd(ipInfo, { callback: ipCallback })
            .catch(err => {
                pending.value = false;
                console.error(`Error checking fingerprint: ${err.message}`);
            });
        const data = res?.data
        await checkRef();

        if (data && data.ipInfo?.ip && data.ipInfo?.ip !== envStore.getIp) {
            const localLocation = SessionStorage.getItem('location');
            /** don't set location based on IP address if they have set it manually already */
            const changeLoc = data.ipInfo.city && (!localLocation?.city || (envStore.ipInfo?.city && envStore.ipInfo.city !== localLocation.city)) || !localLocation.fips;
            envStore.setIp(data.ipInfo);
            if(changeLoc){
                const stateCode = getStateCode(data.ipInfo.region)
                const itemId = `states|${stateCode.toLowerCase()}`
                const jds = await junkStore.find({query: {$limit: 1, itemId}})
                    .catch(err => {
                        console.error(`Could not load state drawer: ${err.message}`);
                        return {data: []}
                    })
                const jd = jds.data[0];
                if(jd) {
                    const cities = jd.data?.cities || {};
                    const cityKeys = Object.keys(cities);
                    const loopCities = (idx: number) => {
                        const cityKey = cityKeys[idx]
                        if (cities[cityKey] && !Array.isArray(cities[cityKey])) {
                            const cityObj = cities[cityKey];
                            envStore.setCity(cityKey)
                            envStore.setLocation({
                                city: cityKey,
                                county: cityObj.county,
                                lngLat: cityObj.lngLat,
                                region: getStateName(stateCode),
                                country: 'US',
                                fips: cityObj.fips,
                                postal: cityObj.zip,
                            })
                        } else if (idx < cityKeys.length - 1) loopCities(idx + 1)
                    }
                    loopCities(0)
                }
            }
        }

        const local = SessionStorage.getItem('location')
        if(local) envStore.setLocation(local)

        const redirect = SessionStorage.getItem('oauth_redirect');
        if (redirect) {
            SessionStorage.removeItem('oauth_redirect')
            window.open(redirect as string);
        }
        pending.value = false
    }

    onMounted(async () => {
       if(autoRun) await runPrint()
        if(afterFn) afterFn()
    })

    return {
        pending,
        envStore,
        fingerprintStore,
        myIpStore,
        route,
        runPrint,
        SessionStorage,
        LocalStorage
    }
}

