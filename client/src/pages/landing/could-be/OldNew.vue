<template>
  <div class="row justify-center q-py-lg">
    <div>
      <!--            <div class="text-center text-sm text-ir-light q-py-md tw-six">Your Care</div>-->
      <div class="__your">
        <age-zip-family @change="reload" @zip-data="setZipData"></age-zip-family>
      </div>
    </div>
  </div>
  <div class="_fw _oh">
    <div class="row justify-center __old">
      <div class="_cent pw2">

        <div class="row pd6">
          <div class="col-12 col-md-6 q-py-sm pw2">
            <div class="row justify-center __p">
              <div class="relative-position">
                <div class="_fw relative-position z4">
                  <div class="__on text-ir-mid">The Old Way</div>
                  <div class="text-xl tw-five text-secondary alt-font">Insurance-Care <span class="text-md">🪦</span></div>
                  <div class="__liner"></div>

                  <div class="__pay">You pay
                    <span
                        class="tw-six alt-font text-s7">{{
                        dollarString((usePlan.premium || fallback.premium) * mult, '$', 0)
                      }}<span class="font-7-8r tw-five">/yr</span></span>
                    for coverage
                    <q-icon v-if="market.silver.data.length > 2" class="text-sm _i_i _hov cursor-pointer" color="s3" name="mdi-information">
                      <q-popup-proxy :breakpoint="30000">
                        <div class="w400 mw100 q-pa-md bg-white">
                          <q-list separator>
                            <q-item-label header>Example Plans</q-item-label>
                            <q-item v-for="(p, i) in market.silver.data.slice(0, 5)" :key="`p-${i}`">
                              <q-item-section>
                                <q-item-label caption class="font-7-8r tw-five text-ir-mid">{{
                                    p.carrierName
                                  }}
                                </q-item-label>
                                <q-item-label class="font-7-8r tw-five">{{ p.title || p.name }}</q-item-label>
                                <q-item-label class="font-3-4r tw-five text-a7">Deductible:
                                  {{ dollarString(p.deductible?.medical?.single?.in_network, '$', 0) }}
                                </q-item-label>
                              </q-item-section>
                              <q-item-section side>
              <span class="tw-six font-1r alt-font text-accent">{{ dollarString(p.premium, '$', 0) }}<span
                  class="text-xxs">/mo</span></span>
                              </q-item-section>
                            </q-item>

                          </q-list>
                        </div>
                      </q-popup-proxy>
                    </q-icon>
                  </div>

                  <div class="row items-center font-1r">
                    <div>
                      That's the "affordable" benchmark in {{ zip }}
                    </div>
                    <!--          <div class="flex items-center no-wrap __fam">-->
                    <!--            <family-member-icon  v-for="(prsn, i) in household.people" :key="`prsn-${i}`" :model-value="prsn" def-size="17px"></family-member-icon>-->
                    <!--          </div>-->
                    <!--          <div>in {{zip}}</div>-->
                  </div>

                  <div>
                    <div class="font-1r q-pt-md text-ir-mid tw-six">Using {{ market.total }} policies in your zip code -
                      from
                    </div>
                    <div>
                      <div class="text-xxs tw-five text-ir-mid q-mt-xs">
                        {{ uniqueCarriers.join(' &#x2219; ') }}
                      </div>
                    </div>

                  </div>

                </div>
              </div>

              <!--          <div class="q-pt-sm row items-center">-->
              <!--            <div class="text-xs tw-five text-ir-mid">Let's see what you've bought</div>-->
              <!--            <div class="q-pa-sm">-->
              <!--              <q-icon class="font-1-1-4r" :name="`mdi-arrow-${$q.screen.lt.md ? 'down' : 'right'}`" color="a7"></q-icon>-->
              <!--            </div>-->
              <!--          </div>-->
            </div>

          </div>
          <div class="col-12 col-md-6 q-py-sm pw2">
            <div class="__ow">
              <div class="__ovr"></div>
              <div class="_fw relative-position z2">
                <div class="q-pb-md text-center text-s4 tw-six font-1-1-4r">One size fits... none?</div>
                <div v-for="(way, i) in oldWays" :key="`ow-${i}`" class="__item">
                  <div class="text-lg">{{ way.icon }}</div>
                  <div v-html="way.text"></div>
                </div>

                <div class="font-1r q-pt-md q-px-md"><span class="text-s7 tw-six">In Summary:</span> You buy insurance
                  for
                  the fear of inaccessible & expensive healthcare
                  - but insurance is what makes it inaccessible and expensive.
                </div>
                <div class="font-1r q-pa-md"><span class="text-s7 tw-six">p.s.</span> Even if your employer "pays" the
                  premium
                  - that's you paying it (it
                  is reported that way on your W2).
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
    <div class="row justify-center __new">
      <div class="_cent pw2">

        <div class="row __p">
          <div class="col-12 col-md-6 q-py-sm pw2">
            <div class="row justify-center __p">
              <div class="relative-position">
                <div class="_fw relative-position z4">
                  <div class="__on">A <i>Better</i> Way</div>
                  <div class="text-xl tw-five alt-font text-primary">Physician-Care 🎯</div>
                  <div class="__liner2"></div>
                  <div class="__pay">You pay <span
                      class="tw-six alt-font text-primary">{{
                      dollarString(dpcCost, '$', 0)
                    }}<span class="font-7-8r tw-five">/yr</span></span> for direct care
                  </div>
                  <div class="__pay">+<span
                      class="tw-six alt-font text-accent">{{
                      dollarString((hs.premium || fallback.premium) * mult, '$', 0)
                    }}<span class="text-xxs tw-five">/yr</span></span> for coverage
                    <q-icon v-if="alts.length > 1" class="text-sm _i_i _hov cursor-pointer" color="a3" name="mdi-information">
                      <q-popup-proxy :breakpoint="30000">
                        <div class="w400 mw100 q-pa-md bg-white">
                          <q-list separator>
                            <q-item-label header>Example Plans</q-item-label>
                            <q-item v-for="(p, i) in alts" :key="`alt-${i}`">
                              <q-item-section>
                                <q-item-label>
                                  <q-chip color="ir-bg2">
                                    <q-avatar v-if="p.carrierLogo">
                                      <img style="object-fit: contain" :src="p.carrierLogo.url">
                                    </q-avatar>
                                    <span>{{ p.carrierName }}</span>
                                  </q-chip>
                                </q-item-label>
                                <q-item-label class="font-7-8r tw-five">{{ p.title || p.name }}</q-item-label>
                                <q-item-label class="font-3-4r tw-five text-p6">OOP Risk:
                                  {{ dollarString(p.deductible?.single, '$', 0) }} per
                                  {{ p.deductible?.type === 'event' ? 'event' : 'year' }}
                                </q-item-label>
                              </q-item-section>
                              <q-item-section side>
              <span class="tw-six font-1r alt-font text-primary">{{ dollarString(p.premium, '$', 0) }}<span
                  class="text-xxs">/mo</span></span>
                              </q-item-section>
                            </q-item>

                          </q-list>
                        </div>
                      </q-popup-proxy>
                    </q-icon>
                  </div>

                  <div class="_fw q-pt-md">
                    <div v-for="(p, i) in points" :key="`p-${i}`">
                      <div class="flex items-center q-pt-xs">
                        <q-icon class="font-1-1-4r" v-if="p[0] === 'up'" color="green" name="mdi-menu-up"></q-icon>
                        <q-icon class="font-1-1-4r" v-else color="red" name="mdi-menu-down"></q-icon>
                        <span class="q-ml-sm font-1r">{{ p[1] }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 q-py-sm pw2">
            <div class="__nw">
              <div></div>
              <div class="__mbr">

                <div class="q-pb-md">
                  Direct Care Memberships<br>
                  <span class="font-1-1-2r alt-font">{{ dollarString(dpcCost, '$', 0) }}<span
                      class="font-7-8r tw-five">/yr</span></span>
                </div>

                <!--            <div>Direct Care for 90% of your needs</div>-->

                <div class="__nl">
                  <div>
                    <div>💸</div>
                    <div>No deductibles or co-pays<br>
                      <span class="__sub">Unlimited visits</span></div>
                  </div>
                  <div>
                    <div>👨‍⚕️</div>
                    <div>Immediate access in-person, virtual, or text<br>
                      <span class="__sub">In most areas house calls too</span>
                    </div>
                  </div>
                  <div>
                    <div>🩻</div>
                    <div>
                      Wholesale labs, meds, and equipment<br>
                      <span class="__sub">Which your doctor can buy for insanely cheap</span>
                    </div>
                  </div>
                </div>

                <div class="q-pt-md font-1r q-pt-md q-px-md">
                  This is <i>quality</i> healthcare - direct access to a doctor who isn't paid to generate transactions.
                  They are
                  at-risk for your care outcomes. They manage all of your care needs - even when you need to navigate
                  secondary care, they connect and negotiate for you.
                </div>
              </div>
            </div>
            <div class="__nc">
              <div></div>
              <div></div>
              <div class="__bod">
                <div>+ Medical Coverage</div>

                <div class="__hs_price">{{ dollarString(hs.premium * mult, '$', 0) }}<span>/yr</span></div>

                <div class="q-pt-md _fw __mc">
                  <div>
                    <div>✅</div>
                    <div>Out of pocket exposure {{ dollarString(hs.deductible?.single, '$', 0) }} per
                      {{ hs.deductible?.type === 'event' ? 'event' : 'year' }}
                    </div>
                  </div>
                  <div>
                    <div>✅</div>
                    <div>Open network - see any doctor</div>
                  </div>
                  <div>
                    <div>✅</div>
                    <div>Cost of care 45% lower than insurance network</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>

  import AgeZipFamily from 'pages/landing/could-be/utils/AgeZipFamily.vue';

  import {sessionFamily} from 'components/households/utils/session-family';
  import {computed, ref, watch} from 'vue';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {useEnvStore} from 'stores/env';
  import {dollarString} from 'src/utils/global-methods';
  import {useMarketplace} from 'stores/marketplace';
  import {slcspMultiples, csrAdjustments, fpl_levels} from 'components/market/utils/rates';
  import {pointToGeo} from 'src/utils/geo-utils';
  import {getFixedRateKey, getPremium} from 'components/coverages/utils/display';
  import {useCoverages} from 'stores/coverages';


  const junkStore = useJunkDrawers();
  const envStore = useEnvStore();
  const marketStore = useMarketplace();
  const coverageStore = useCoverages();

  const emit = defineEmits(['update:alt', 'update:silver'])

  const { age, household, address } = sessionFamily(envStore, {})

  const mult = ref(12);
  const zipData = ref({})
  const zip = computed(() => address.value?.postal || '27283')


  const slcsp = ref({})
  const basePremium = 295;
  const fallback = computed(() => {
    let premium = 0;
    for (const person of household.value.people.slice(0, 5)) {
      const add = slcspMultiples[person.age] * basePremium;
      if (!isNaN(add)) premium += add
    }
    const fpl = fpl_levels[household.value.people.length];
    const pct_of_fpl = ((household.value.income || 200000) / fpl) * 100;
    let csr = 1;
    /** get the csr multiplier as a percentage of standard silver oop - which is .3. So this means at the .94 CSR level, you'd multiply by .06/.3 which is .2 */
    for (const k in csrAdjustments) {
      if (pct_of_fpl <= Number(k)) {
        csr = (1 - csrAdjustments[k]) / .3;
        break;
      }
    }
    const single = 5500 * csr;
    const family = 12500 * csr;
    return {
      premium,
      deductible: {
        medical: {
          single: { in_network: single },
          family: { in_network: family }
        }
      }
    }
  })

  const usePlan = computed(() => {
    if (slcsp.value?._id) return slcsp.value;
    else return fallback.value;
  })

  const market = ref({ silver: { data: [] } })
  const health_shares = ref({ data: [] })
  const off_market = ref({ data: [] })

  const uniqueCarriers = computed(() => ((market.value.facet_groups?.length ? market.value.facet_groups : market.value.silver?.facet_groups)?.find(a => a.name === 'issuers')?.facets || []).map(a => a.value))

  const alts = computed(() => {
    const enrolled = household.value.people;
    const ages = enrolled.map(a => a.age)
    const key = getFixedRateKey({ enrolled }).key
    // console.log('key', key);
    const list = [];
    for (let i = 0; i < health_shares.value.data.length; i++) {
      const sh = health_shares.value.data[i]
      list.push({ ...sh, premium: getPremium(sh, key, { ages }) })
    }
    for (let i = 0; i < off_market.value.data.length; i++) {
      const sh = off_market.value.data[i]
      list.push({ ...sh, premium: getPremium(sh, key, { ages }) })
    }
    return list.sort((a, b) => a.premium - b.premium)
  })
  const hsAmts = { 1: 160, 2: 310, 3: 450, 4: 450, 5: 450, 6: 450, 7: 450, 8: 450, 9: 450, 10: 450, 11: 450, 12: 450 }
  const hs = computed(() => {
    const plan = alts.value[0]
    if (!plan) return { premium: (hsAmts[household.value.people.length]) || 230 * slcspMultiples[age.value || 40] }
    return plan;
  })

  const reloadId = ref('')
  const reload = async () => {
    const pplId = household.value.people.map(a => a.age).join('|')
    const income = household.value.income;
    const fips = zipData.value.fips;
    const newReloadId = `${pplId}|${income}|${fips}`
    if (newReloadId !== reloadId.value) {
      reloadId.value = newReloadId;
      const promises = [];
      promises.push(marketStore.find({
        query: {
          // filter: { metal_levels: ['Silver'] },
          household: household.value,
          place: { zipcode: zipData.value.zip, countyfips: zipData.value.fips, state: zipData.value.state }
        }, runJoin: { quick_quote: { limit: 5 } }
      })
          .then(res => market.value = res)
          .catch(err => {
            console.error(`Error finding slcsp: ${err.message}`);
            return { data: [], silver: { data: [] } }
          }))
      const query = {
        $or: [{ geo: { $exists: false } }],
        type: 'hs',
        // public: true,
        // sim: true,
        $limit: 5,
        $sort: { fortyPremium: 1 }
      }
      if (zipData.value.lngLat) query.$or.push({ ['geo.geometry']: { $geoIntersects: { $geometry: pointToGeo(zipData.value.lngLat)?.geometry } } })
      promises.push(coverageStore.find({ query, runJoin: { add_files: ['carrierLogo']} })
          .then(res => {
            health_shares.value = res;
          })
          .catch(err => console.error(`Error finding health shares: ${err.message}`)))
      promises.push(coverageStore.find({ runJoin: { add_files: ['carrierLogo']}, query: { ...query, type: 'mm' } }).then(res => off_market.value = res)
          .catch(err => console.error(`Error finding off market: ${err.message}`)))
      await Promise.all(promises)

      slcsp.value = market.value.silver.data[1] || market.value.silver.data[0] || {}

    }
  }

  const setZipData = (val) => {
    zipData.value = val;
    reload()
  }

  watch(zip, (nv, ov) => {
    setTimeout(async () => {
      if (nv && nv !== ov && !zipData.value?.zip) {
        await junkStore.find({ query: { itemId: `zips|${nv.substring(0, 3)}` } })
            .then(res => {
              setZipData({ ...res.data[0].data[nv], zip: nv });
            })
            .catch(err => console.error(`Error loading location: ${err.message}`))
      }
    }, 1000)

  }, { immediate: true })

  const dpcCost = computed(() => {
    const costs = {
      1: 95,
      2: 150,
      3: 210,
      4: 275,
      5: 300,
      6: 300,
      7: 300,
      8: 300,
      9: 300,
      10: 300,
      11: 300,
      12: 300
    }
    return (costs[household.value.people.length] || 300) * mult.value
  })

  const points = [
    ['up', 'Immediate access to care'],
    ['up', 'Individualized care - invested doctor'],
    ['down', 'Patients per doctor - 75% less'],
    ['down', 'Cost of care, meds, labs, supplies - 90% less'],
  ]

  const oldWays = computed(() => [
    {
      icon: '🤔',
      text: 'Insurance dictates the schedule of care<br><span class="font-7-8r tw-six text-ir-mid">Long wait times - short visit times</span>'
    },
    {
      icon: '😱',
      text: `<span class="alt-font">${dollarString(usePlan.value.deductible?.medical.single?.in_network, '$', 0)}</span> deductible<br><span class="font-7-8r tw-six text-ir-mid">+ 20% coinsurance & 20% chance of denial</span>`
    },
    {
      icon: '💀',
      text: 'The network cost of care is 213% of the cash price <br><span class="font-7-8r tw-six text-ir-mid">which you can confirm now thanks to price transparency laws</span>'
    }
  ])

  const optionsString = computed(() => `${alts.value[0]?._id}-${market.value.silver.data[0]?._id}`)

  watch(optionsString, (nv, ov) => {
    if (nv !== ov) {
      emit('update:alt', alts.value[0] || {})
      emit('update:silver', market.value.silver.data[0])
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

  .__old {
    background: radial-gradient(var(--q-s0), white 80%);
  }
  .__new {
    background: radial-gradient(var(--q-p0), white 80%);
  }

  .__ways {
    width: 100%;

    > div {
      &:first-child {
        font-size: 2rem;
        font-weight: 600;
        text-align: center;
        text-transform: uppercase;
        font-family: var(--alt-font);
      }

      &:nth-child(2) {
        font-size: 1.4rem;
        font-weight: 600;
        text-align: center;
      }
    }
  }

  .__on {
    font-weight: 500;
    color: var(--ir-mid);
    font-size: var(--text-md);
    line-height: 1.4rem;
  }

  .__pay {
    font-size: var(--text-lg);
  }

  .__your {
    margin: 20px 0;
    padding: 10px;
    border-radius: 10px;
    background: white;
    max-width: 95vw;
    width: 500px;
    box-shadow: -14px -14px 28px var(--q-p0), 14px 14px 28px var(--q-s0);
  }

  .__fam {
    background: var(--ir-bg2);
    padding: 2px 4px;
    border-radius: 3px;
    margin: 0 2px;
  }

  .__liner {
    width: 600px;
    max-width: 90vw;
    height: 1px;
    background: var(--q-s4);
    margin: 8px 0;
  }

  .__ow {
    width: 100%;
    padding: 35px max(10px, 1.5vw);
    //border: dashed 4px var(--q-a2);
    border-radius: min(3vw, 17px);
    position: relative;
    background-color: white;
    background-image: repeating-linear-gradient(
            180deg,
            var(--q-a1) 0px,
            var(--q-a1) 2px,
            transparent 2px,
            transparent max(1.5vw, 22px)
    );
    //background: repeating-linear-gradient(
    //        -45deg,
    //        var(--q-a1) 0px,
    //        var(--q-a1),
    //        white 14px,
    //        white 9px);

    .__ovr {
      position: absolute;
      z-index: 0;
      top: 0;
      left: 0;
      border-radius: inherit;
      height: 100%;
      width: 100%;
      //background: linear-gradient(135deg, white, transparent);

    }

    .__item {
      padding: 15px 0;
      display: grid;
      grid-template-columns: auto 1fr;
      align-items: center;
      line-height: 1.4rem;
      //border-radius: 8px;
      //box-shadow: 8px 8px 18px var(--ir-bg2);

      > div {
        padding: 5px 10px;
        font-size: 1.1rem;


        &:nth-child(2) {
          font-weight: 500;
        }
      }
    }
  }

  .__p {
    padding: max(15px, 2vw) 0;
  }

  .__nw {
    width: 100%;
    padding: 35px max(10px, 1.5vw);
    border-radius: min(3vw, 17px) min(3vw, 17px) 0 0;
    position: relative;
    background-color: white;
    background-image: repeating-linear-gradient(
            90deg,
            var(--q-p1) 0px,
            var(--q-p1) 2px,
            transparent 2px,
            transparent max(1.5vw, 22px)
    ),
    repeating-linear-gradient(
            180deg,
            var(--q-p1) 0px,
            var(--q-p1) 2px,
            transparent 2px,
            transparent max(1.5vw, 22px)
    );

    > div {
      &:first-child {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        z-index: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 1), transparent);
      }
    }

    .__mbr {
      position: relative;
      z-index: 1;

      > div {
        &:first-child {
          text-align: center;
          font-weight: 600;
          font-size: 1.25rem;
          color: var(--q-p5);
        }

        &:nth-child(2) {
          font-size: 1.1rem;
          font-weight: 500;
        }
      }

      .__nl {
        > div {
          display: grid;
          grid-template-columns: auto 1fr;
          padding: 15px 0;
          align-items: center;
          //border-bottom: dashed 4px var(--q-p1);

          > div {
            padding: 5px 10px;
            font-size: 1.1rem;
            font-weight: 500;
          }

          &:first-child {
            font-size: 1.4rem;
          }

          &:nth-child(2) {
            line-height: 1.4rem;
          }

          .__sub {
            font-size: .88rem;
            font-weight: 600;
            color: var(--ir-mid);
          }
        }
      }

    }
  }

  .__liner2 {
    height: 1px;
    background: var(--q-p4);
    margin: 8px 0;
    width: 600px;
    max-width: 90vw;
  }

  .__nc {
    width: 100%;
    padding: 35px max(10px, 1.5vw);
    border-radius: 0 0 min(3vw, 17px) min(3vw, 17px);
    position: relative;
    overflow: hidden;
    background: white;

    > div {
      &:first-child, &:nth-child(2) {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }

      &:first-child {
        z-index: 0;
        background: repeating-linear-gradient(
                180deg,
                var(--q-a1) 0px,
                var(--q-a1) 2px,
                transparent 2px,
                transparent max(1.5vw, 22px)
        )
        //background: linear-gradient(135deg, var(--q-a0), transparent);
      }

      &:nth-child(2) {
        z-index: 1;
        opacity: .8;
        //background: linear-gradient(315deg, white, transparent 100%);
      }
    }

    .__bod {
      position: relative;
      z-index: 2;

      > div {
        &:first-child {
          text-align: center;
          font-weight: 600;
          font-size: 1.25rem;
          color: var(--q-accent);
          font-family: var(--alt-font);
        }
      }

      .__hs_price {
        text-align: center;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--q-a5);
        font-family: var(--alt-font);

        span {
          font-size: .75rem;
          font-weight: 500;
        }
      }

      .__mc {
        > div {
          display: grid;
          grid-template-columns: auto 1fr;
          padding: 4px 0;
          align-items: center;
          //border-bottom: dashed 4px var(--q-p1);

          > div {
            padding: 5px 10px;
            font-size: 1.1rem;
            font-weight: 500;

            &:first-child {
              font-size: 1.4rem;
              font-weight: 500;
            }
          }

        }

      }
    }
  }

</style>
