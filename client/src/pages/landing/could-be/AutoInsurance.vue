<template>
  <div class="__auto_ins">
    <div id="inner">
      <div class="__scenes">
        <div class="__scene" v-for="(scene, i) in scenes" :key="`scene-${i}`">
          <q-img class="_fa" fit="contain" :src="scene"></q-img>
        </div>
      </div>
    </div>
    <div class="__l">
      <q-btn @click="scroll(-1)" dense flat icon="mdi-chevron-left" color="white"></q-btn>
    </div>
    <div class="__r">
      <q-btn @click="scroll(1)" dense flat icon="mdi-chevron-right" color="white"></q-btn>
    </div>
  </div>
</template>

<script setup>

  const scenes = [
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fauto-insurance%2Fscene1.png?alt=media&token=f38f8979-3792-44d2-86c9-8e58cbe8bad0',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fauto-insurance%2Fscene2.svg?alt=media&token=49bc3780-aa3a-4f0a-aba3-99c4cf1c9248',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fauto-insurance%2Fscene3.svg?alt=media&token=b7924ad3-e545-4f49-a14c-20b6b3c1a514',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fauto-insurance%2Fscene4.svg?alt=media&token=757f0828-7cf9-4b19-9847-abf6c23c67fc',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fauto-insurance%2Fscene5.svg?alt=media&token=637296ac-21bc-4c4c-9ca8-4a8414eff5e7',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fauto-insurance%2Fscene6.svg?alt=media&token=852ca3f3-d584-405c-a3a9-516003ee16eb'
  ]

  const scroll = (v) => {
    const el = document.querySelector('#inner');
    if(el){
      el.scrollTo({
        left: el.scrollLeft + (v * Math.min(window.innerWidth * .94, 700)),
        behavior: 'smooth'
      });
    }
  }
</script>

<style lang="scss" scoped>
  .__auto_ins {
    width: 100%;
    position: relative;
    overflow-x: hidden;
    border-radius: 10px;
    border: solid 10px var(--q-p2);
    background: var(--q-p2);

    > div:first-child {
      position: relative;
      width: 100%;
      overflow-x: scroll;


      .__scenes {
        width: auto;
        display: grid;
        grid-template-rows: min(94vw, 700px);
        grid-template-columns: repeat(6, min(700px, 94vw));
        grid-gap: 10px;

        .__scene {
          width: 100%;
          height: 100%;
          transition: all .4s;
          background: linear-gradient(170deg, var(--q-p1), white);

        }

      }
    }
    .__l, .__r {
      position: absolute;
      top: 50%;
      transform: translate(0, -50%);
      background: rgba(0,0,0,.5);
      display: grid;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      cursor: pointer;
    }

    .__l {
      left: 10px;
    }
    .__r {
      right: 10px;
    }
  }
</style>
