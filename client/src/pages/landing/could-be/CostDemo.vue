<template>
  <div class="__cost_demo">
    <div class="q-pb-sm font-1r text-ir-deep tw-five">Matching cash prices for your bill</div>
    <table>
      <thead>
      <tr>
        <th v-for="(item, i) in headers" :key="`item-${i}`">{{ item }}</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(b, i) in bills" :key="`bill-${i}`">
        <td :class="classes[idx]" v-for="(d, idx) in b" :key="`dt-${i}-${idx}`">{{ format[idx](d) }}</td>
      </tr>
      </tbody>
    </table>

    <table class="q-mt-sm">
      <tbody>
      <tr class="__r">
        <td></td>
        <td></td>
        <td></td>
        <td>Your bill</td>
        <td>
          <span class="alt-font tw-six">{{dollarString(total, '$', 0)}}</span>
        </td>

      </tr>
      <tr class="__r">
        <td></td>
        <td></td>
        <td></td>
        <td>Savings found</td>
        <td>
          <span class="alt-font tw-six text-primary">{{dollarString(savings, '$', 0)}}</span>
        </td>
      </tr>
      <tr class="__r">
        <td></td>
        <td></td>
        <td></td>
        <td>New bill</td>
        <td>
          <span class="alt-font tw-six text-accent">{{dollarString(total - savings, '$', 0)}}</span>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>

  import {dollarString} from 'src/utils/global-methods';

  const headers = ['Date', 'Rev Code', 'Desc', 'Billed', 'Best Price']
  const def = (v) => v
  const ds = (v) => dollarString(v, '$', 2)
  const format = [def,def,def,ds, ds]
  const classes = ['','','','','__sv']
  const date = '4/3/25'
  const bills = [
    [date, '0636', 'HC or Time 181-210', 4220.1, 1540.65],
    [date, '0636', 'Fentanyl per 0.1 Mg', 205.9, 80],
    // [date, '0272', 'Thrombin (Recombinant)', 708, 145.50],
    [date, '0922', 'HC Intra OP Monitoring', 2146.70, 680],
    [date, '0360', 'Propofol 10 Mg/Ml Emul', 209.15, 90.20]
  ]

  const getTotals = () => {
    let total = 0;
    let savings = 0;
    for(const b of bills){
      savings += b[3] - b[4]
      total += b[3]
    }
    return { total, savings };
  }
  const totes = getTotals()
  const total = totes.total
  const savings = totes.savings
</script>

<style lang="scss" scoped>

  .__cost_demo {
    width: 100%;
    overflow: scroll;
  }
  table {
    font-size: .85rem;
    width: 100%;
    tr {
      td, th {
        padding: 4px 8px;
        text-align: left;
        border-bottom: solid 1px var(--ir-light);
      }

      td {
        font-family: var(--alt-font);
      }

      &:last-child {
        td {
          border-bottom: none
        }
      }
    }
    .__r {
      td {
        font-weight: 500;
        border-bottom: none;
        &:first-child {
          width: 40%
        }
      }
    }
  }

  .__sv {
    color: var(--q-primary);
    font-weight: 600;
  }

</style>
