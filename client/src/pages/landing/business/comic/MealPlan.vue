<template>
  <div class="__meal_plan">
    <div id="meal_plan">
      <div class="__scenes">
        <div class="__scene" v-for="(scene, i) in scenes" :key="`scene-${i}`">
          <q-img class="_fa" fit="contain" :src="scene"></q-img>
        </div>
      </div>
    </div>
    <div class="__l">
      <q-btn @click="scroll(-1)" dense flat icon="mdi-chevron-left" color="white"></q-btn>
    </div>
    <div class="__r">
      <q-btn @click="scroll(1)" dense flat icon="mdi-chevron-right" color="white"></q-btn>
    </div>
  </div>
</template>

<script setup>

  const scenes = [
     'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene1.png?alt=media&token=bdce4d98-498d-4d77-8f64-23452ba6dc11',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene2.png?alt=media&token=4a06a4da-8b27-4a8d-a20c-1fa7e43fbde6',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene3.png?alt=media&token=2296d838-2b44-4d4b-baf2-2e443bb417fe',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene4.png?alt=media&token=798f262c-7435-4b4d-b668-05c75d5f6d5e',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene5.png?alt=media&token=dbf08c1a-a3ff-479d-b938-a5b5172bb7c6',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene6.png?alt=media&token=9db38e79-bc2b-4606-9ae5-f2843f4015c5',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene7.png?alt=media&token=e42de5d4-83f3-4354-a74c-f0ac845db070'
  ]

  const scroll = (v) => {
    const el = document.querySelector('#meal_plan');
    if(el){
      el.scrollTo({
        left: el.scrollLeft + (v * Math.min(window.innerWidth * .94, 600)),
        behavior: 'smooth'
      });
    }
  }
</script>

<style lang="scss" scoped>
  .__meal_plan {
    width: 100%;
    position: relative;
    overflow-x: hidden;
    border-radius: 10px;
    //border: solid 10px var(--q-a1);
    //background: var(--q-a1);

    > div:first-child {
      position: relative;
      width: 100%;
      overflow-x: scroll;


      .__scenes {
        width: auto;
        display: grid;
        grid-template-rows: min(94vw, 450px);
        grid-template-columns: repeat(7, min(600px, 94vw));
        grid-gap: 10px;

        .__scene {
          width: 100%;
          height: 100%;
          transition: all .4s;
          //background: linear-gradient(170deg, var(--q-a1), white);

        }

      }
    }
    .__l, .__r {
      position: absolute;
      top: 50%;
      transform: translate(0, -50%);
      background: rgba(0,0,0,.5);
      display: grid;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      cursor: pointer;
    }

    .__l {
      left: 10px;
    }
    .__r {
      right: 10px;
    }
  }
</style>
