<template>
  <q-page class="_fw text-ir-off">
    <div class="row justify-center relative-position z2">
      <div class="_cent">
        <div class="pd15 pw2">
          <div class="row">
            <div class="col-12 col-md-6 pw2 q-pb-xl">
              <div class="text-xxl tw-six text-primary">Join the <span class="__roots">grassroots</span> takeover
                of <span class="__care">healthcare</span></div>
              <q-separator class="q-my-sm" color="p3"></q-separator>
              <div class="text-sm tw-seven text-ir-deep q-pt-xs">Reform doesn't need to come from Washington - in-fact
                it's better if it doesn't
              </div>
            </div>
            <div class="col-12 col-md-6 q-pa-md">
              <div class="__invite">
                <invite-card></invite-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row justify-center relative-position">
      <div class="__blob"></div>
      <div class="__blob1"></div>
      <div class="__blob2"></div>
      <div class="_cent pd12 pw2">
        <div class="relative-position q-px-md z1 text-lg tw-five q-pb-xl text-ir-deep">How we
          <q-chip dense square color="primary" class="text-lg tw-six _i_i text-white">want</q-chip>
          healthcare to look
        </div>
        <q-tab-panels :model-value="!!active" class="_panel" animated>
          <q-tab-panel class="_panel" :name="false">
            <div class="row">
              <div class="col-12 col-md-4 q-pa-sm" v-for="(k, i) in Object.keys(wants)" :key="`want-${i}`">
                <div class="__c" @click="active = k" :style="`background-image: url(${wants[k].img})`">
                  <div class="__cover"></div>
                  <div class="__label">
                    <div>{{ wants[k].label }}</div>
                    <div>{{ wants[k].label }}</div>
                  </div>
                </div>
              </div>
            </div>
          </q-tab-panel>
          <q-tab-panel class="_panel" :name="true">
            <div class="__news">
              <div class="t-l">
                <q-btn dense flat icon="mdi-close" color="secondary" @click="active = ''"></q-btn>
              </div>
              <div class="q-px-md">
                <div class="text-sm tw-six">{{ wants[active].label }}</div>
              </div>
              <div class="q-pa-md __content" v-html="wants[active].text"></div>
            </div>
          </q-tab-panel>
        </q-tab-panels>

      </div>
    </div>
    <div class="_fw relative-position z1">

      <div class="__wave">
        <div></div>
      </div>
      <div class="row justify-center pd4">
        <div class="_cent pw4">
          <div class="row justify-center q-pb-xl">
            <q-chip dense square color="primary" class="text-white tw-six text-lg">How we're making it happen</q-chip>
          </div>

          <div class="_fw __sec" v-for="(w, i) in Object.keys(wants)" :key="`w-${i}`" @click="program === w ? program = '' : program = w">
            <div>{{ wants[w].label }} with</div>
            <div>{{ wants[w].solution }}</div>
<!--            <div>{{ wants[w].preview }}</div>-->
            <div></div>
            <div :class="program === w ? '' : '__off'">
              <div v-html="wants[w].longview"></div>
            </div>
            <div class="_fw">
              <q-chip dense clickable color="transparent"
                     class="tw-five text-accent" :label="`Read ${program === w ? 'Less' : 'More'}`"></q-chip>
            </div>
            <q-img :src="wants[w].icon" class="__img" fit="contain"></q-img>

          </div>
        </div>
      </div>
    </div>
    <div class="_fw bg-p0">
      <div class="row justify-center">
        <div class="_cent pw2 pd8">
          <div class="row q-py-lg">
            <div class="col-12 col-md-6 pw2 q-py-md text-ir-deep tw-six">
              <div class="text-xl">We're taking back healthcare <span class="__supply">supply</span> by organizing <span class="__demand">demand</span>.</div>
              <q-separator class="q-my-sm"></q-separator>
              <div class="text-sm tw-five">If we lower the friction to an absolute minimum, and operate 100%
                transparently, it will stoke the trust missing in our current system.
              </div>

              <div class="text-sm tw-six text-center text-ir-deep q-pt-lg q-pb-md">Membership fee: <q-chip dense square color="primary" class="text-white tw-six text-sm">$10/mo per member (max $30/family) <span class="text-xs">&nbsp;(PMPM)</span></q-chip></div>

              <div class="row justify-center q-py-md">
                <div class="__pie">
                  <div></div>
                  <div></div>
                </div>
              </div>
              <div class="row justify-center">
                <div class="text-sm tw-six text-ir-deep">
                  <div class="flex items-center no-wrap">
                    <q-avatar size="15px" color="primary"></q-avatar>
                    <div class="q-pl-sm">$2 network share (social sellers)</div>
                  </div>
                  <div class="flex items-center no-wrap q-py-sm">
                    <q-avatar size="15px" color="p2"></q-avatar>
                    <div class="q-pl-sm">$8 network cost (tech and hosting)</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-6 q-py-md pw2">

              <seller-profile-preview></seller-profile-preview>

            </div>
          </div>
        </div>
      </div>

    </div>
    <div class="_fw relative-position z1 q-pt-xl">
      <social-selling></social-selling>
    </div>
  </q-page>
</template>

<script setup>
  import SocialSelling from './SocialSelling.vue';
  import SellerProfilePreview from 'pages/landing/refs/SellerProfilePreview.vue';
  import InviteCard from 'components/refs/cards/InviteCard.vue';

  import {ref} from 'vue';
  import {wants} from './utils/sections';


  const active = ref('');
  const program = ref('')

</script>

<style lang="scss" scoped>
  .__invite {
    z-index: 2;
    border-radius: 15px;
    padding: 30px max(2vw, 10px);
    //box-shadow: 0 0 0 4px var(--q-p2), 8px -8px 28px rgba(0,0,0,.1);
    background: white;
    box-shadow: 18px -18px 38px -8px var(--q-p1);
    //background: linear-gradient(135deg, white, var(--q-p0));
  }
  .__cl {
    background: linear-gradient(180deg, var(--q-primary), var(--q-p6));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .__roots {
    position: relative;
    display: inline-block;

    &::after {
      content: "grassroots";
      top: 50%;
      left: 0;
      transform: rotatex(180deg) translateY(5%);
      filter: blur(1px);
      mask-image: repeating-linear-gradient(transparent, transparent 3px, white 3px, white 4px), linear-gradient(0deg, transparent 40%, white, transparent 30%);

      //content: "";
      position: absolute;
    }
  }

  .__care {
    position: relative;
    display: inline-block;
    color: var(--q-secondary);

    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 15%; /* Moves the underline up */
      width: 100%;
      height: 1.5rem; /* Thickness of the line */
      background-color: var(--q-s1); /* Change to any color */
      z-index: -1;
    }
  }

  .__c {
    position: relative;
    border-radius: 10px;
    box-shadow: 0 2px 8px var(--ir-light);
    padding: 25px;
    cursor: pointer !important;
    height: min(250px, 70vw);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: bottom;
    //font-family: var(--alt-font);
    color: var(--ir-text);
    transition: all .4s;

    &:hover {
      transform: translate(0, -5px);

      .__label {
        transform: scale(1.154);
      }
    }

    .__cover {
      cursor: pointer;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: inherit;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, .9));
    }

    .__label {
      position: absolute;
      bottom: 0;
      left: 0;
      padding-bottom: 20px;
      width: 100%;
      cursor: pointer;
      transition: all .4s;

      > div {
        font-size: var(--text-md);
        font-weight: 800;
        text-align: center;
        background: white;
        background-clip: text;
        color: transparent;

        &:last-child {
          transform: rotatex(180deg) translatey(35%);
          //mask-image: white;
          //filter: blur(2.9px);
          mask-image: radial-gradient(circle at center, white, transparent 50%);
          filter: blur(1px);
          //mask-image: repeating-linear-gradient(transparent, transparent 3px, white 3px, white 4px), linear-gradient(transparent 50%, white 90%);
          //mask-image: linear-gradient(transparent 50%, white 90%);

        }
      }
    }
  }

  .__news {
    background: rgba(255, 255, 255, 1);
    //box-shadow: 0 4px 12px var(--ir-light);
    margin: 5px;
    position: relative;
    padding: 45px max(2vw, 5px);
    border-radius: 10px;

    .__content {
      font-size: var(--text-xs);
      font-weight: 400;
      font-family: var(--alt-font);
      //column-rule: 1px solid var(--ir-mid);
      text-align: justify;
      column-gap: 35px;
      column-width: 380px;
      //column-count: 3;
    }
  }

  .__blob {
    pointer-events: none;
    position: absolute;
    z-index: 0;
    width: 100vw;
    height: 130vw;
    border-radius: 50%;
    opacity: 0;
    background: radial-gradient(var(--q-a2) -50%, transparent 50%);
    transform: translate(5%, 0);
    animation: roam 20s infinite;
    top: -100%;
    left: -30%;
  }

  .__blob1 {
    pointer-events: none;
    position: absolute;
    z-index: 0;
    width: 90vw;
    height: 70vw;
    border-radius: 50%;
    opacity: 1;
    background: radial-gradient(var(--q-p2) -50%, transparent 50%);
    transform: translate(5%, 0);
    animation: roam 20s infinite;
    top: -70%;
    left: -10%;
  }

  .__blob2 {
    pointer-events: none;
    position: absolute;
    z-index: 0;
    width: 90vw;
    height: 70vw;
    border-radius: 50%;
    opacity: 1;
    background: radial-gradient(var(--q-s3) -50%, transparent 50%);
    transform: translate(5%, 0);
    animation: roam 20s infinite;
    bottom: -30%;
    right: -10%;
  }

  .__sec {
    z-index: 3;
    max-width: 100%;
    position: relative;
    min-height: 200px;
    padding: 50px max(2vw, 20px);
    overflow: hidden;
    margin: 0px 0;
    border-radius: 20px;
    box-shadow: 0 -14px 16px rgba(0, 0, 0, .1);
    background: var(--ir-bg);
    transition: all .3s;

    &:hover {
      transform: translate(0, -5px);
    }

    .__img {
      width: 250px;
      height: 250px;
      position: absolute;
      z-index: 0;
      opacity: .15;
      top: 50%;
      left: 80%;
      transform: translate(-50%, -50%);
    }

    > div {
      font-weight: 600;

      &:first-child {
        font-size: var(--text-xs);
        color: var(--ir-mid);
        font-family: var(--alt-font);
        line-height: 1.2rem;
      }

      &:nth-child(2) {
        font-size: var(--text-md);
      }

      &:nth-child(3) {
        font-size: var(--text-xs);
      }

      &:nth-child(4) {
        transition: all .6s;
        max-height: 800px;
        padding: 10px 0;
        font-weight: 500;
        font-size: var(--text-xs);
        text-shadow: 0 1px 1px white;
      }
    }
  }

  .__off {
    max-height: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  }


  .__wave {
    position: absolute;
    top: 300px;
    left: 0;
    bottom: 0;
    width: 100vw;
    background: var(--ir-p);
    z-index: 1;

    > div {
      position: relative;
      height: 100%;
      width: 100%;

      &::after {
        position: absolute;
        z-index: 1;
        content: "";
        top: 0;
        left: -15%;
        width: 65%;
        height: 200px;
        border-radius: 50%;
        transform: translate(0, -50%);
        background: var(--ir-p);
      }
      &::before {
        position: absolute;
        z-index: 1;
        content: "";
        top: 0;
        right: -15%;
        width: 65%;
        height: 200px;
        border-radius: 50%;
        transform: translate(0, -50%);
        background: var(--ir-bg);
      }
    }

  }

  .__supply, .__demand {
    position: relative;
    z-index: 2;

    &::after {
      position: absolute;
      content: "";
      left: 0;
      top: 50%;
      width: 100%;
      height: .5ch;
      background: var(--q-s2);
      transform: translate(0, 20%);
      z-index: -1;
    }
  }
  .__demand {
    &::after {
      background: var(--q-p2);

    }
  }


  .__pie {
    width: 250px;
    height: 250px;
    max-width: 80vw;
    max-height: 80vw;
    border-radius: 50%;
    overflow: visible;
    background: conic-gradient(
            transparent 0% 20%, /* Green for 60% */
            var(--q-p3) 20% 100% /* Orange for 40% */
    );

    > div {
      width: 100%;
      height: 100%;
      border-radius: 50%;

      &:first-child {
        z-index: 1;
        transform: translate(6px, -2px) scale(1.1);
        background: conic-gradient(
                var(--q-primary) 0% 20%, /* Green for 60% */
                transparent 20% 100%);
      }

      &:nth-child(2) {

      }
    }

  }

</style>
