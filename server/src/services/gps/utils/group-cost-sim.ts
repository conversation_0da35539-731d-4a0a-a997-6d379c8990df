import {HookContext} from '../../../declarations.js';
import {CoreCall, loadExists, setExists} from 'feathers-ucan';
import {fakeId, getStateCode, pointToGeo} from '../../../utils/index.js';
import {getCoverageRate, getFixedRateKey} from '../../enrollments/utils/index.js';
import {ObjectId} from 'mongodb';
import {cmsMarketplaceSearch, nonMarketplaceStates} from '../../marketplace/cms/index.js'

type TotalSims = {
    ees: Array<any>,
    householdByEe: { [key: string]: any },
    shopObj: { [key: string]: any },
    gps: any
}
const totalSims = ({ees, householdByEe, shopObj, gps}: TotalSims) => {
    return async (context: HookContext) => {

        const shops = {...shopObj}
        const missingShopIds = ees.map(a => a.sim).filter(a => !!a && !shops[a]);
        if (missingShopIds.length) {
            const missingShops = await new CoreCall('shops', context).find({
                query: {
                    _id: {$in: missingShopIds},
                    $limit: missingShopIds.length
                }
            })
            for (const shop of missingShops.data) shops[shop._id] = shop;
        }
        // const prmDef = () => {
        //     return {
        //         single: 0,
        //         plus_spouse: 0,
        //         plus_child: 0,
        //         plus_child__2: 0,
        //         plus_child__3: 0,
        //         family: 0
        //     }
        // }
        const currentStats: any = {
            count: 0,
            premium: 0,
            // premiumByKey: {...prmDef()},
            // countByKey: {...prmDef()},
            spend: 0,
            spendCount: 0,
            spendPremium: 0
        };
        const simBase: any = () => {
            return {
                count: 0,
                spend: 0,
                premium: 0,
                premiumPtc: 0,
                spendPtc: 0,
                tax_savings: 0,
                tax_rate: 0,
                // premiumByKey: {...prmDef()},
                // premiumByKeyPtc: {...prmDef()},
                ptc: 0,
                ptc_likely: 0,
                // countByKey: {...prmDef()},
                // altByKey: {...prmDef()},
                altSpend: 0,
                altPremium: 0,

                selected_ptc: 0,
                selected_ptc_delta: 0,

                selected_premium: 0,
                selected_premium_delta: 0,

                selected_spend: 0,
                selected_spend_delta: 0,
                // selected_premiumByKey: {...prmDef()},
                selected_count: 0,

                selected_tax_savings: 0,
                selected_tax_savings_delta: 0
            }
        }
        const inactiveBase = () => {
            const obj = {};
            const base = {...simBase()}
            for (const k in base) {
                obj[`inactive_${k}`] = base[k];
            }
            return obj
        }
        const simStats: any = {
            ...simBase(),
            ...inactiveBase()
        };

        for (const ee of ees) {
            const enrolled = householdByEe[ee.uid].people
            // const frKey = getFixedRateKey({enrolled})

            let premium;
            let prefix = '';
            if (ee.coverage) {
                const cov = (gps.coverages || {})[ee.coverage]
                if (cov) {
                    premium = getCoverageRate({
                        coverage: cov,
                        enrolled,
                        // def_key: frKey.key,
                        def_age: ee.age
                    })

                    if (premium) {
                        currentStats.count++
                        currentStats.premium += premium * 12
                        // currentStats.countByKey[frKey.key]++;
                        // currentStats.premiumByKey[frKey.key] += premium * 12;
                    }
                }
            } else prefix = 'inactive_';
            if (ee.sim) {
                let shop = shops[ee.sim];
                if (!shop.coverages?.length) {
                    const household = { people: [{ age: shop.stats.age, gender: shop.stats.gender }, ...shop.stats.people || []], income: shop.stats.income, place: shop.stats.place }
                    shop = await new CoreCall('shops', context).get(ee.sim, {
                        query: {
                            place: household.place,
                            household
                        },
                        runJoin: {
                            cost_sim: {
                                skip_aptc: !!gps.ale,
                                stats: shop.stats,
                                household
                            }
                        }
                    })
                        .catch(err => {
                            console.log(`Could not patch shop for cost sim: ${err.message}`)
                            return shop;
                        })
                }
                if(!shop?.stats.place || !shop.spend) continue
                simStats[`${prefix}ptc`] += (shop.aptc || 0) * 12;
                // simStats[`${prefix}spend`] += shop.spend || 0
                simStats[`${prefix}count`]++;
                simStats[`${prefix}tax_rate`] += shop.tax_rate || 0;

                let best: any = {};
                let bestPtc: any = {};
                let alt: any = {};
                /** loop through and set best and alt coverages */
                for (const cov of shop.coverages || []) {
                    const covId = cov._id || cov.id
                    if (covId === ee.coverage) {
                        currentStats.spend += shop.coverage_scores[covId].average;
                        currentStats.spendCount++;
                        currentStats.spendPremium += shop.coverage_scores[covId].premium;
                    }
                    const orig = shop.coverage_scores[covId];
                    const origPtc = shop.coverage_scores_ptc[covId];
                    const obj = {
                        _id: covId,
                        average: orig.average,
                        premium: orig.premium,
                        acaPlan: cov.acaPlan,
                        tax_savings: orig.tax_savings
                    };
                    const objPtc = {
                        _id: covId,
                        average: origPtc.average,
                        premium: origPtc.premium,
                        acaPlan: cov.acaPlan,
                        tax_savings: origPtc.tax_savings
                    };
                    if (cov.type === 'hs') {
                        if (!alt.average || alt.average > obj.average) alt = obj;
                        // else {
                        //     if (obj.premium < best.premium) best = obj;
                        //     if (obj.premium < bestPtc.premium) bestPtc = obj;
                        // }
                    } else {
                        if (!best.average || best.average > obj.average) best = obj;
                        if (!bestPtc.average || bestPtc.average > objPtc.average) bestPtc = objPtc;
                    }
                }
                const likely_ptc = best.acaPlan ? (shop.aptc || 0) * 12 : 0
                simStats[`${prefix}ptc_likely`] += likely_ptc;
                const w_ptc = shop.coverage_scores_ptc[bestPtc._id];
                if (w_ptc) {
                    simStats[`${prefix}premiumPtc`] += w_ptc.premium || 0;
                    simStats[`${prefix}spendPtc`] += w_ptc.average || 0;
                    // simStats[`${prefix}premiumByKeyPtc`][frKey.key] += w_ptc.premium;
                }
                simStats[`${prefix}premium`] += best.premium || 0;
                simStats[`${prefix}spend`] += best.average || 0;
                // simStats[`${prefix}premiumByKey`][frKey.key] += best.premium;
                simStats[`${prefix}tax_savings`] += best.tax_savings || 0;
                // simStats[`${prefix}countByKey`][frKey.key]++;
                simStats[`${prefix}altSpend`] += alt.average || 0;
                simStats[`${prefix}altPremium`] += alt.premium || 0;
                // simStats[`${prefix}altByKey`][frKey.key] += alt.premium || 0;
                let selected = shop.coverage || shop.policy;
                if (selected) {
                    const selectedScore = shop.coverage_scores[selected];
                    if (selectedScore) {
                        simStats[`${prefix}selected_tax_rate`] += shop.tax_rate || 0;
                        let selected_ptc = 0;
                        if (selected.acaPlan) {
                            selected_ptc = (shop.aptc || 0) * 12;
                        }
                        /** set ptc likely consumed by the selected plan - remove what was likely consumed by the recommended plan from the delta */
                        simStats[`${prefix}selected_ptc`] += selected_ptc;
                        simStats[`${prefix}selected_ptc_delta`] += likely_ptc;

                        simStats[`${prefix}selected_count`]++;
                        simStats[`${prefix}selected_premium`] += selectedScore.premium || 0;
                        simStats[`${prefix}selected_premium_delta`] += best.premium || 0;
                        // simStats[`${prefix}selected_premiumByKey`][frKey.key] += selectedScore.premium;
                        simStats[`${prefix}selected_tax_savings`] += selectedScore.tax_savings || 0;
                        simStats[`${prefix}selected_tax_savings_delta`] += selectedScore.tax_savings || 0;

                        simStats[`${prefix}selected_spend`] += selectedScore.average || 0;
                        simStats[`${prefix}selected_spend_delta`] += best.average || 0;
                        if (w_ptc) {
                            simStats[`${prefix}selected_premiumPtc_delta`] += w_ptc.premium || 0;
                            simStats[`${prefix}selected_spendPtc_delta`] += w_ptc.average || 0;
                        }
                    }
                }
                if(best) ee.bestPlan = String(best._id);
                if(bestPtc) ee.bestPlanPtc = String(bestPtc._id);
                if(alt) ee.altPlan = String(alt._id);
            }

        }
        simStats.selected_tax_rate = (simStats.selected_tax_rate || 0) / (simStats.selected_count || 1)
        simStats.tax_rate = simStats.tax_rate / (simStats.count || 1)
        simStats.inactive_tax_rate = simStats.inactive_tax_rate / (simStats.inactive_count || 1)
        for (const k in simStats) {
            if (isNaN(simStats[k])) delete simStats[k]
        }
        for (const k in currentStats) {
            if (isNaN(currentStats[k])) delete currentStats[k]
        }
        return {ees, simStats, currentStats};
    }
}

export const getZip = (zip: any, zipData: any) => {
    if (!zipData) return;
    const tryZip = (z: any, tries = 0) => {
        const obj = zipData[z];
        if (obj && typeof obj === 'object') return obj;
        else if (tries < 50) return tryZip(Number(z) + 1, tries + 1)
        else return;
    }
    return tryZip(zip, 0);
}

export const retotalSims = async (context: HookContext) => {
    if (context.params.runJoin?.re_total_sims) {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        const {employees = []} = ex;
        const zips = employees.map(a => a.zip).filter(a => !!a);
        const zipIds = Array.from(new Set(zips.map(a => `zips|${a.slice(0, 3)}`)));
        const zipDrawers = await new CoreCall('junk-drawers', context).find({
            query: {
                itemId: {$in: zipIds},
                $limit: zipIds.length
            }
        })
        const zipObj: any = {};
        for (const d of zipDrawers.data) {
            zipObj[d.itemName] = d.data;
        }
        const householdByEe: any = {}

        for (const ee of employees) {
            const zipData = getZip(ee.zip, zipObj[ee.zip.slice(0, 3)])
            if (!zipData) {
                ee.simError = 'Invalid zip code'
                continue;
            }
            const household: any = {place: {zipcode: ee.zip, countyfips: zipData.fips, state: zipData.state}};
            const people: any = [{age: ee.age, relation: 'self', gender: ee.gender || 'male'}];
            if (ee.married?.toLowerCase().includes('Y')) people.push({
                age: ee.spouseAge || ee.age,
                gender: ee.gender === 'male' ? 'female' : 'male',
                relation: 'spouse'
            });
            for (let i = 0; i < ee.deps || 0; i++) {
                people.push({age: 10, relation: 'child', child: true, gender: 'male'})
            }
            household.people = people;
            if (ee.income) household.income = ee.hh_income || ee.income;
            householdByEe[ee.uid] = household;
        }

        const shopIds = employees.map(a => a.sim).filter(a => !!a);
        const shops = await new CoreCall('shops', context).find({query: {_id: {$in: shopIds}, $limit: shopIds.length}})
        const shopObj: any = {}
        for (const shop of shops.data) shopObj[shop._id] = shop;

        const {ees, currentStats, simStats} = await totalSims({
            ees: employees,
            householdByEe,
            shopObj,
            gps: ex
        })(context);

        context.data = {...context.data, employees: ees, simStats, currentStats};
    }
    return context;
}

export const runGroupCostSim = () => {
    return async (context: HookContext) => {
        const {cost_sim} = context.params.runJoin || {};
        if (cost_sim) {

            let {exclude = [], employees, exclude_issuers, limit, risk, cms_issuers} = cost_sim;
            const ex = await loadExists(context);
            context = setExists(context, ex);

            const {coverages, plan: planId, host, ale} = ex;
            let plan: any = {};
            if (planId) {
                plan = await new CoreCall('plans', context).get(planId, {admin_pass: true})
                    .catch(err => console.log(`Could not get plan for cost sim: ${err.message}`))
            }
            const removeShops: any = []
            if (!employees) {
                employees = ex.employees;
                for (const ee of ex.employees) if (ee.sim) removeShops.push(ee.sim);
            }
            const zips = employees.map(a => a.zip).filter(a => !!a);
            const zipIds = Array.from(new Set(zips.map(a => `zips|${a.slice(0, 3)}`)));
            const zipDrawers = await new CoreCall('junk-drawers', context).find({
                query: {
                    itemId: {$in: zipIds},
                    $limit: zipIds.length
                }
            })
            const zipObj: any = {};
            for (const d of zipDrawers.data) {
                zipObj[d.itemName] = d.data;
            }

            const shopObj: any = {};
            const householdByEe: any = {};

            // const eeCoverages:any = {};
            // const eeCtx:any = {};
            const coveragesByFips: any = {};
            const query: any = {
                $or: [{geo: {$exists: false}}],
                type: {$in: ['hs', 'mm']},
                sim: true,
                public: true,
                $limit: 40,
                $sort: {fortyPremium: 1},
                fortyPremium: {$exists: true}
            }
            if (plan?._id) {
                if (!cost_sim.showPublic) delete query.public;
                query._id = {$in: Object.keys(plan.coverages || {}).map(a => ObjectId.createFromHexString(a))}
            }
            if (exclude_issuers) query.carrierName = {$nin: exclude_issuers}

            const acaStateList = zips.map(a => getStateCode(zipObj[a.slice(0, 3)]?.state)).filter(a => !nonMarketplaceStates.includes(a));

            let progress = ex.simProgress || 0;

            for (let idx = 0; idx < employees.length; idx++) {
                const ee = employees[idx];
                const zipData = getZip(ee.zip, zipObj[ee.zip.slice(0, 3)])
                if (!zipData || typeof zipData !== 'object') {
                    ee.simError = 'Invalid zip code'
                    continue;
                }
                const household: any = {place: {zipcode: ee.zip, countyfips: zipData.fips, state: zipData.state}};
                const people: any = [{age: ee.age, relation: 'self', gender: ee.gender || 'male'}];
                if (ee.married?.toLowerCase().includes('y')) people.push({
                    age: ee.spouseAge || ee.age,
                    gender: ee.gender === 'male' ? 'female' : 'male',
                    relation: 'spouse'
                });
                for (let i = 0; i < ee.deps || 0; i++) {
                    people.push({age: 10, relation: 'child', child: true, gender: 'male'})
                }
                household.people = people;
                if (ee.income) household.income = ee.hh_income || ee.income;
                householdByEe[ee.uid] = household;
                if (idx <= progress) continue
                const eeQuery: any = {...query}
                const byFips = coveragesByFips[household.countyfips]
                if (byFips) eeQuery._id = {$nin: Object.keys(byFips)}
                eeQuery.$or.push({['geo.geometry']: {$geoIntersects: {$geometry: pointToGeo(zipData.lngLat)?.geometry}}})

                const private_policies = await new CoreCall('coverages', context)._find({
                    skip_hooks: true, admin_pass: true,
                    query: eeQuery
                })

                if (!byFips) coveragesByFips[household.countyfips] = {};
                for (const cov of private_policies.data) {
                    coveragesByFips[household.countyfips][cov._id] = cov;
                }
            }

            let limit_remaining = 1000;
            const oneSim = async (emp: any, idx: number) => {
                if (idx <= progress) return emp;
                const ee = {...emp}
                if (limit_remaining < 5) {
                    progress = Math.min(progress, idx);
                    ee.simError = 'Rate limit exceeded'
                    return ee;
                }
                progress = idx;
                if (!ee.zip) {
                    ee.simError = 'No zip code provided'
                    return ee;
                }
                let shopId = ee.sim;
                const newShop: any = {}
                if (ee.person) newShop.person = ee.person;
                if (planId) newShop.plan = planId;
                if (host) newShop.host = host;
                const aptc = ale === false ? true : ale ? false : employees.length <= 50;

                const household = householdByEe[ee.uid];
                if (!household?.place?.countyfips) {
                    ee.simError = 'No county fips provided'
                    return ee;
                }

                let simError = undefined
                const pass_private_policies = Object.keys(coveragesByFips[household.countyfips]).map(a => coveragesByFips[household.countyfips][a]);
                const sp: any = [];
                const usableCoverageIds = Object.keys(coverages).filter(a => !exclude.includes(a));
                const shop = await new CoreCall('shops', context).get(shopId || fakeId, {
                    query: {$limit: 150, household, place: household.place},
                    runJoin: {
                        cost_sim: {
                            cms_issuers,
                            static_plans: sp.filter(a => !!a),
                            pass_private_policies,
                            tax_rate: .06,
                            skip_aptc: !aptc,
                            compare_ids: usableCoverageIds,
                            coverages: usableCoverageIds.map(a => {
                                return {
                                    ...coverages[a],
                                    _id: a
                                }
                            }),
                            data: newShop,
                            stats: {
                                people: household.people.filter(a => a.relation !== 'self'),
                                age: ee.age,
                                gender: ee.gender || 'male',
                                place: household.place,
                                income: household.income || 1
                            },
                            household,
                            risk: ee.risk || 5
                        }
                    }
                })
                    .catch(err => {
                        simError = err.message;
                        console.log(`Error running sim for employee: ${ee.firstName + ' ' + ee.lastName} - ${err.message}`)
                    })
                if (shop?._id) {
                    limit_remaining = shop.limit_remaining || 0;
                    ee.sim = shop._id;
                    shopObj[shop._id] = shop
                } else ee.simError = simError
                return ee;
            }

            const ees = await Promise.all(employees.map((ee, i) => oneSim(ee, i)))

            const {ees: ees2, simStats, currentStats} = await totalSims({ees, householdByEe, shopObj, gps: ex})(context)

            if (removeShops.length) await new CoreCall('shops', context).remove(null, {query: { _id: { $in: removeShops }}, disableSoftDelete: true})
                .catch(err => console.log(`Error removing replaced shops after group cost sim: ${err.message}`))

            let simProgress = progress;
            if (progress >= employees.length - 1) simProgress = 0;

            context.result = await new CoreCall('gps', context).patch(context.id as any, {
                employees: ees2,
                simStats,
                simProgress,
                currentStats,
                lastSim: new Date()
            });

        }
        return context;
    }
}
