// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Cats, CatsData, CatsPatch, CatsQuery, CatsService } from './cats.class.js'

export type { Cats, CatsData, CatsPatch, CatsQuery }

export type CatsClientService = Pick<CatsService<Params<CatsQuery>>, (typeof catsMethods)[number]>

export const catsPath = 'cats'

export const catsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const catsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(catsPath, connection.service(catsPath), {
    methods: catsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [catsPath]: CatsClientService
  }
}
