// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import type {Application} from '../../declarations.js'
import {MyIpService, getOptions} from './my-ip.class.js'
import {myIpPath, myIpMethods} from './my-ip.shared.js'
import { getClientIp } from '../../utils/ip-utils.js'
import type { HookContext } from '../../declarations.js'

export * from './my-ip.class.js'

// A configure function that registers the service and its hooks via `app.configure`
export const myIp = (app: Application) => {
    // Register our service on the Feathers application
    app.use(myIpPath, new MyIpService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: myIpMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(myIpPath).hooks({
        around: {
            all: []
        },
        before: {
            all: [],
            find: [],
            get: [],
            create: [],
            patch: [],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [myIpPath]: MyIpService
    }
}
