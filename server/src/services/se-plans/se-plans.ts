// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    sePlansDataValidator,
    sePlansPatchValidator,
    sePlansQueryValidator,
    sePlansResolver,
    sePlansExternalResolver,
    sePlansDataResolver,
    sePlansPatchResolver,
    sePlansQueryResolver
} from './se-plans.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {SePlansService, getOptions} from './se-plans.class.js'
import {sePlansPath, sePlansMethods} from './se-plans.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';

export * from './se-plans.class.js'
export * from './se-plans.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['se-plans', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['se-plans', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        create: [...writer],
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*'
    })(context)
}

const addStatePlanId = (context: HookContext) => {
    context.data.state_plan_id = `${context.data.state_code.toLowerCase()}:${context.data.business_year}:${context.data.plan_id}`;
    return context;
}
import {stateExchangeDataUpload, getPremium} from './utils/index.js';
import {ratingAreaUpload} from './utils/rating-areas.js';


const setFortyPremiumAndZips = async (context: HookContext) => {
    if (context.params.skip_hooks || !context.result._id) return context;
    if (!context.result || context.params.fortyLoop) return context;
    const p = getPremium(context.result);
    const patchObj: any = {}
    if (p) patchObj.fortyPremium = p

    const getAllZips = (a: any) => {
        if (!a.rating_areas) return { zips: [], fips: []};
        const zips: any = [];
        const fips: any = []
        for (const k in a.rating_areas) {
            const ra = a.rating_areas[k];
            for (let i = 0; i < (ra.zips || []).length; i++) {
                zips.push(ra.zips[i])
            }
            for (let i = 0; i < (ra.fips || []).length; i++) {
                fips.push(ra.fips[i])
            }
        }
        return {zips, fips}
    }

    const all_zips = getAllZips(context.result);
    const first_3_zips = all_zips.zips.map(a => a.slice(0, 3))
    patchObj.all_zips = all_zips.zips;
    patchObj.first_3_zips = first_3_zips;
    patchObj.all_fips = all_zips.fips

    context.result = await new CoreCall('se-plans', context).patch(context.result._id, patchObj, {fortyLoop: true})
        .catch(err => {
            console.log(`Error patching forty premium and zips on record ${context.result._id}: ${err.message}`)
            return context.result
        })
    return context;
}


import multer from 'multer';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    req.feathers.file = req.file;
    return next();
}
// A configure function that registers the service and its hooks via `app.configure`
export const sePlans = (app: Application) => {
    // Register our service on the Feathers application
    app.use(sePlansPath,
        restMiddleware,

        new SePlansService(getOptions(app)), {
            // A list of all methods this service exposes externally
            methods: sePlansMethods,
            // You can add additional custom events to be sent to clients here
            events: []
        })
    // Initialize hooks
    app.service(sePlansPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(sePlansExternalResolver),
                schemaHooks.resolveResult(sePlansResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(sePlansQueryValidator),
                schemaHooks.resolveQuery(sePlansQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                stateExchangeDataUpload,
                ratingAreaUpload,
                (context: HookContext) => {
                    if (!context.result) {
                        schemaHooks.validateData(sePlansDataValidator)(context);
                        schemaHooks.resolveData(sePlansDataResolver)(context);
                        addStatePlanId(context)
                    }
                    return context
                }
            ],
            patch: [
                schemaHooks.validateData(sePlansPatchValidator),
                schemaHooks.resolveData(sePlansPatchResolver)
            ],
            remove: []
        },
        after: {
            all: [],
            patch: [setFortyPremiumAndZips],
            create: [setFortyPremiumAndZips],
            find: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [sePlansPath]: any
    }
}
