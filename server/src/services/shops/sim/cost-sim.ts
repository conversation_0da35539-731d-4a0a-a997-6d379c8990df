import {HookContext} from '../../../declarations.js';
import {cmsMarketplaceGet, cmsMarketplaceSearch} from '../../marketplace/cms/index.js';
import {CoreCall} from 'feathers-ucan';
import {getCoverageRate} from '../../enrollments/utils/index.js';
import {pointToGeo, fakeId} from '../../../utils/index.js';
import {ObjectId} from 'mongodb';
import {quickTaxTotal} from '../../../utils/tax/tax-calcs.js';
// import {category_weights, CoCategories} from 'src/services/marketplace/utils/index.ts';

export const getResetId = (shot: any) => {
    const {age, people, risk, smoker, place, income} = shot?.stats || {}
    const pplId = (people || []).map(a => a.age).join('|')
    const fips = place?.countyfips || ''
    return `${pplId}|${income}|${fips}|${age}|${risk}|${smoker}`
}

const costData = {
    /** each age key is the highest age of that spend - so every age between the last breakpoint and the key is the same as the key */
    byAge: {
        18: 1500,
        34: 3500,
        44: 5500,
        54: 7500,
        64: 9500,
        120: 12000
    },
    zeroOdds: {
        18: 55,
        34: 45,
        44: 35,
        54: 25,
        64: 5,
        120: 0,
    },
    /** each key is a percentile of how much a class of people spend relative to everyone else ie: key "1" means spends more than 99% of the population. The values are their percentage of total spend */
    bySpend: {
        1: .27,
        5: .52,
        10: .68,
        15: .77,
        20: .84,
        50: .99
    },
    range: {
        max_spend: 200000,
        min_spend: 0
    }
}
export type Household = {
    people: Array<{
        age: number,
        child: boolean
    }>
}

type Limit = {
    single: number,
    family: number
}
const costLimits: { [key: string]: Limit } = {
    moop: {
        single: 9200,
        family: 18400
    },
    deductible: {
        single: 9200,
        family: 18400
    }
}

const filterDeds = (deds: Array<any>): Array<any> => {
    if (!deds?.length) return [];
    return deds.filter(a => a.network_tier === 'In-Network' || !a.network_tier).sort((a, b) => {
        // Check if `network_tier` is defined for both a and b
        const aHasTier = a.network_tier !== undefined;
        const bHasTier = b.network_tier !== undefined;

        // Prioritize items with `network_tier` defined
        if (aHasTier && !bHasTier) return -1;
        if (!aHasTier && bHasTier) return 1;
        return 0
    })
}
type CoinsOptions = {
    policy: any
}
type OopRes = {
    coinsurance: number,
    deductible: { single: number, family: number },
    moop: { single: number, family: number }
}
const orZero = (v: any, def: Array<any>) => {
    if (v || v === 0) return v;
    else return def.filter(a => !!a)[0] || 0;
}
const getAcaOop = ({policy}: CoinsOptions): OopRes => {
    const moop = policy.moops || costLimits.moop
    const ded = policy.deductible || {medical: {}}
    const deductible = {
        family: orZero(ded.medical.family?.in_network, [ded.medical.single?.in_network, costLimits.deductible.family]),
        single: orZero(ded.medical.single?.in_network, [costLimits.deductible.single])
    }
    if (deductible.single && !deductible.family) deductible.family = deductible.single;
    else if (deductible.family && !deductible.single) deductible.single = deductible.family;
    // const ded = oopType === 'family' ? deds.filter(a => a.family)[0]?.amount : deds.filter(a => !a.family && a.type.includes('Medical'))[0]?.amount);
    let coinsurance = 0;
    // let copays = 0;
    let insAcc = 0;
    // let payAcc = 0;
    const bens: any = (policy.benefits || []);
    for (let i = 0; i < bens.length; i++) {
        const costs = bens[i].cost_sharings || []
        for (let cs = 0; cs < costs.length; cs++) {
            const cost_share = costs[cs];
            if (cost_share.network_tier === 'In-Network') {
                if (cost_share.coinsurance_rate && Number(cost_share.coinsurance_rate) <= 1) {
                    coinsurance += Number(cost_share.coinsurance_rate);
                    insAcc++
                }
                // } else if(cost_share.copay_amount) {
                // payAcc++;
                // copays += cost_share.copay_amount
                // }
            }
        }
    }
    if (bens.length) {
        coinsurance = coinsurance / (insAcc || 1)
        // copays = (copays / (payAcc || 1)) * 3 //assume 3 annual visits
    } else {
        coinsurance = .3
    }
    return {coinsurance: coinsurance || coinsurance === 0 ? coinsurance : 1, moop, deductible}
}

const getPrivateOop = ({policy}: CoinsOptions): OopRes => {
    let {coinsurance, moop, deductible, coins} = policy || {};

    if (!moop) {
        if (policy.type === 'mm') moop = costLimits.moop
        else moop = {
            family: orZero(deductible?.family, [costLimits.moop.family]),
            single: orZero(deductible?.single, [costLimits.moop.single])
        }
    }

    //TODO: consider blending in other coins rates with category_weights
    const {amount} = coinsurance || {};
    return {coinsurance: amount || amount === 0 ? amount : 1, moop, deductible: {...costLimits.moop, ...deductible}}
    // const dedSpend = Math.min(ded, spend);
    // const coins = (spend - dedSpend) * (coinsurance || 0)
    // const all = dedSpend + coins
    // return Math.min(all, max || all)
}

const getEvents = () => {
    const random = Math.random();
    if (random < 0.62) {
        return 1; // 62% likelihood
    } else if (random < 0.92) {
        return 2; // 30% likelihood (62% + 30%)
    } else {
        return 3; // 8% likelihood (remaining probability)
    }
}

type SimOpts = {
    household: Household & any,
    // ded: number,
    // fDed: number,
    // dedType: 'annual'|'event',
    // moop: number,
    // fMoop: number,
    // coins :number,
    coverages: Array<any>,
    isAcaPlan?: boolean,
    risk?: number,
    key: 'single' | 'family' | 'plus_spouse' | 'plus_child' | 'plus_child__2' | 'plus_child__3'
    coverage_limits: { [key: string]: { moop: Limit, deductible: Limit, coinsurance: number } }
}
const simulateHealthcareSpend = ({household, coverages, key, risk, coverage_limits}: SimOpts) => {

    const getOop = (spend: number, familyAcc: number, {moop, deductible, coinsurance}: {
        moop: Limit,
        deductible: Limit,
        coinsurance: Number
    }) => {

        /** get oop against accumulated, deductible, copay, and moop */
        if (key === 'single') {
            const ded = Math.min(spend, orZero(deductible?.single, [deductible?.family]))
            const coins = (spend - ded) * (Number(coinsurance) <= 1 ? Number(coinsurance) : 0)
            return Math.min(moop?.single || moop?.family, ded + coins)
        } else {
            const spd = spend;
            const familyDed = Math.max(0, deductible?.family - (familyAcc || 0));
            const ded = Math.min(spd, orZero(deductible?.single, [familyDed]), familyDed)
            const coins = (spd - ded) * (Number(coinsurance || 0) <= 1 ? Number(coinsurance) : 0)
            const familyMoop = (moop?.family || moop?.single) - (familyAcc || 0);
            return Math.max(0, Math.min(ded + coins, (moop?.single || moop?.family), familyMoop))
        }
    }

    const getAgeSpend = (age: number, risk: number) => {
        for (const ageKey in costData.byAge) {
            if (age <= Number(ageKey)) {
                return costData.byAge[ageKey] * riskMultiples[risk];
            }
        }
        return costData.byAge[120] * riskMultiples[risk];
    };
    const getZeroOdds = (age) => {
        for (const maxAge in costData.zeroOdds) {
            if (age <= parseInt(maxAge, 10)) {
                return costData.zeroOdds[maxAge]; // Convert percentage to probability
            }
        }
        return costData.zeroOdds[40]; // Default to the highest age bracket
    };

    const getRandomSpendMultiplier = () => {
        const rand = Math.random();
        const check = Math.random();
        if (check < .011) return 70 * rand;
        else if (check < .051) return 10 * rand;
        else if (check < .11) return 8 * rand;
        else if (check < .2) return 3 * rand;
        else if (check < .3) return 2 * rand;
        else if (check < .4) return rand;
        else if (check < .5) return rand;
        else if (check < .6) return rand;
        else if (check < .6) return rand / 3;
        else if (check < .7) return rand / 5;
        else if (check < .8) return rand / 10;
            // else if(check < .9) return rand / 20;
        // else if(check < .95) return rand / 10;
        else return rand / 10;
    };


    const riskMultiples = {
        1: .1,
        2: .2,
        3: .35,
        4: .6,
        5: .85,
        6: 1.2,
        7: 1.6,
        8: 2.5,
        9: 6,
        10: 12
    }

    const skewMultiplier = () => {
        // Power-law distribution to skew spend towards high values
        return .85 // Adjust the exponent to control the skew
    };

    const hhCount = household.people.length;
    type PersonSpend = {
        age,
        byCvg: { [key: string]: number },
        bankedSpend: number,
        events: number
    }
    const simulatePersonSpend = ({age, byCvg, bankedSpend, events}: PersonSpend) => {
        const baseSpend = getAgeSpend(age, risk || 5);
        const spendMultiplier = getRandomSpendMultiplier() * skewMultiplier();
        const simulatedSpend = baseSpend * spendMultiplier;

        let bank = 0;
        // Clamp the spend between min and max spend
        let spend = Math.max(costData.range.min_spend, Math.min(costData.range.max_spend, simulatedSpend));
        if (spend < baseSpend * .5) {
            const zeroProbability = getZeroOdds(age);
            if (Math.random() < zeroProbability && spend < 300) {
                bank = spend + bankedSpend;
                const byCoverage: any = {};
                for (let i = 0; i < coverages.length; i++) {
                    byCoverage[coverages[i]._id] = 0;
                }
                return {bank, spend: 0, byCoverage};
            } else if (spend > baseSpend * hhCount) {
                spend += bankedSpend;
            }
        } else {
            spend += bankedSpend;
        }

        const res: any = {
            bank: 0,
            spend,
            byCoverage: {}
        }

        for (let i = 0; i < coverages.length; i++) {
            const coverage = coverages[i];
            const limits = coverage_limits[coverage._id]
            if (coverage.deductible?.type === 'event') {
                let oop = 0;
                let mult = [1];
                if (events > 1) {
                    /** split up the total randomly among the events */
                    const one = Math.random();
                    const rest = (1 - one) / (events - 1);
                    mult[0] = one;
                    for (let rIdx = 1; rIdx < events; rIdx++) {
                        mult.push(rest)
                    }
                }
                for (let evt = 0; evt < events; evt++) {
                    const spd = spend * mult[evt]
                    const op = getOop(spd, 0, limits)
                    oop += op;
                }
                res.byCoverage[coverage._id] = oop || 0;
            } else {
                const oop = getOop(spend, byCvg[coverage._id], limits);
                res.byCoverage[coverage._id] = oop || 0;
            }
        }
        return res;
    };


    let totalSpend = 0;
    let byCoverageId: { [key: string]: number } = {};
    let bankedSpend = 0;

    const hhEventCount = getEvents() * (Math.max(1, hhCount * .5))

    /** iterate through each household member, bank zero spend simulations, register oop spend by coverage id and pass it as the family deductible/moop accumulator */
    for (let i = 0; i < hhCount; i++) {
        const {spend, byCoverage, bank} = simulatePersonSpend({
            age: household.people[i].age,
            byCvg: byCoverageId,
            bankedSpend,
            events: i === 0 ? hhEventCount : 1
        })
        totalSpend += spend;
        for (const k in byCoverage) {
            byCoverageId[k] = Math.round((byCoverageId[k] || 0) + (byCoverage[k] || 0))
        }
        bankedSpend = bank;
    }

    return {spend: Math.round(totalSpend), byCoverageId};
};

/**
 * RUN COST SIM
 *
 * Configuration options for runJoin.cost_sim:
 * @param {Object} data - Base data for the simulation, will be merged with shop data if available
 * @param {Object} limit - Limits for the simulation results
 * @param {Object} household - Household information containing people with age and child status
 * @param {number} risk - Risk factor for cost simulation (affects spending calculations)
 * @param {Array} exclude_issuers - Array of issuer IDs to exclude from the simulation
 * @param {Array} cms_issuers - Array of CMS issuer IDs to include in the simulation
 * @param {boolean} cms_plans - Whether to include CMS marketplace plans in the simulation
 * @param {boolean} ichra - If true, only include major medical (mm) plans; otherwise include health share (hs) plans too
 * @param {boolean} showPublic - If true, include public plans even when a specific plan is selected
 */
export const runCostSim = () => {
    return async (context: HookContext) => {
        const {cost_sim} = context.params.runJoin || {};
        if (cost_sim) {
            let shopData = cost_sim.data;
            if (context.id !== fakeId) {
                const shop = await new CoreCall('shops', context).get(context.id as any, {admin_pass: true})
                    .catch(err => console.log(`Could not get shop for cost sim: ${err.message}`))
                if (shop) shopData = {...shopData, ...shop};
            }
            let plan: any = {};
            if (shopData.plan) {
                const pl = await new CoreCall('plans', context).get(shopData.plan, {admin_pass: true})
                    .catch(err => console.log(`Could not get plan for cost sim: ${err.message}`))
                if (pl) plan = pl;
            }
            const {limit, household, risk, exclude_issuers, cms_issuers, pass_private_policies, static_plans} = cost_sim;
            context.params.quick_quote = {limit}
            if (cms_issuers?.length) context.params.query.filter = {
                ...context.params.query.filter,
                issuers: cms_issuers
            }
            context.params.query = {...context.params.query, $limit: 60}
            const ctx = cost_sim.cms_plans || !plan.ale ? await cmsMarketplaceSearch(context) : {
                result: {
                    limit_remaining: 1000,
                    gold: {data: []},
                    silver: {data: []},
                    bronze: {data: []}
                }
            };

            let private_policies = { data: (pass_private_policies || []).sort((a, b) => (b.fortyPremium || Infinity) - a.fortyPremium)
            };
            if(!pass_private_policies) {

                const zip = context.params.query.place.zipcode
                const query: any = {
                    $or: [{geo: {$exists: false}}],
                    type: {$in: cost_sim.ichra ? ['mm'] : ['hs', 'mm']},
                    sim: true,
                    group_sim_only: {$ne: true},
                    public: true,
                    $limit: 25,
                    $sort: {fortyPremium: 1},
                    fortyPremium: {$exists: true}
                }
                if (cost_sim.compare_ids) {
                    delete query.group_sim_only
                }
                if (plan._id) {
                    if (!cost_sim.showPublic) delete query.public;
                    query._id = {$in: Object.keys(plan.coverages || {}).map(a => ObjectId.createFromHexString(a))}
                }
                if (exclude_issuers) query.carrierName = {$nin: exclude_issuers}
                const drawers = await new CoreCall('junk-drawers', context).find({
                    query: {
                        $limit: 1,
                        itemId: `zips|${zip.substring(0, 3)}`
                    }
                })
                    .catch(err => {
                        console.log(`Error finding cost sim drawer: ${err.message}`)
                        return {}
                    })
                if (drawers.total) {
                    const getObj = (z: any, tries = 0) => {
                        const obj = drawers.data[0].data[z];
                        if (obj && typeof obj === 'object') return obj;
                        else if (tries < 50) return getObj(Number(z) + 1, tries + 1)
                        else return {}
                    }
                    let obj = getObj(zip, 0);
                    query.$or.push({['geo.geometry']: {$geoIntersects: {$geometry: pointToGeo(obj.lngLat)?.geometry}}})
                }

                private_policies = await new CoreCall('coverages', context)._find({
                    skip_hooks: true, admin_pass: true,
                    query
                })
            }

            let children = 0;
            let spouse = -1;
            const enrolled: any = [];
            for (const prsn of household.people) {
                if (prsn.child || spouse > 0) {
                    children++
                    enrolled.push({age: prsn.age, relation: 'child'})
                } else {
                    enrolled.push({age: prsn.age, relation: spouse > -1 ? 'spouse' : 'self'})
                    spouse++
                }
            }

            const key = children ? spouse > 0 ? 'family' : `plus_children${children === 1 ? '' : children > 2 ? '__3' : '__2'}` : spouse > 0 ? 'plus_spouse' : 'single' as any

            const loaded_coverages: any = cost_sim.coverages || [];

            for (let i = 0; i < 10; i++) {
                if ((ctx.result.gold.data || [])[i]?.premium) loaded_coverages.push(ctx.result.gold.data[i])
                if ((ctx.result.silver.data || [])[i]?.premium) loaded_coverages.push(ctx.result.silver.data[i])
                if ((ctx.result.bronze.data || [])[i]?.premium) loaded_coverages.push(ctx.result.bronze.data[i])
            }

            for (let i = 0; i < 25; i++) {
                if (private_policies.data[i]) loaded_coverages.push(private_policies.data[i])
            }



            if (cost_sim.compare_ids) {
                const search_ids: any[] = [];
                const coverageIds = loaded_coverages.map(a => a._id || a.id);
                for (const id of cost_sim.compare_ids) {
                    if (!coverageIds.includes(id)) search_ids.push(id)
                }
                if (search_ids.length) {
                    const got = await cmsMarketplaceGet(search_ids, household, context.params.query.place || household.place)(context)
                        .catch(err => {
                            console.log(`Error getting compare ids for cost sim: ${err.message}`)
                            return []
                        })
                    for (const cov of got) loaded_coverages.push(cov)
                }
            }

            let coverages = static_plans;
            if(!coverages?.length) {
                coverages = [];
                for (const cov of loaded_coverages) {
                    coverages.push(cov);
                }
            }


            type CoverageScores = {
                [key: string]: {
                    top1: number, //had the lowest total spend
                    top3: number, //had top3 lowest spend
                    last: number, //had the worst spend
                    pr: number, //best outcome for this coverage (range min)
                    pr_spend: number, //spend total on best outcome
                    pw: number, //worst outcome for this coverage (range max)
                    pw_spend: number, //spend for worst outcome for this coverage
                    premium: number, //premium for this coverage
                    aptc_eligible_premium: number, //premium for this coverage
                    average: number, //average outcome
                    median: number, //median outcome
                    aptc: number,
                    spend: number,
                    oop: number,
                    oop_ptc: number,
                    total: number,
                    total_ptc: number,
                    premium_ptc: number,
                    tax_savings: number,
                    moop: Limit,
                    deductible: Limit,
                    coinsurance: number
                }
            }
            const coverage_scores: CoverageScores = {}
            const coverage_scores_ptc: CoverageScores = {}

            const distributionKeys = [1000, 5000, 10000, 20000, 50000, 100000]
            type DistKey = '*' | '1000' | '5000' | '10000' | '20000' | '50000' | '100000'
            type Distribution = {
                [key in DistKey]?: {
                    [key: string]: number;
                };
            };
            const distribution: Distribution = {'*': {}}
            const dcount: Distribution = {'*': {}}
            const distribution_ptc: Distribution = {'*': {}}
            const dcount_ptc: Distribution = {'*': {}}
            for (let i = 0; i < distributionKeys.length; i++) {
                distribution[String(distributionKeys[i])] = {};
                dcount[String(distributionKeys[i])] = {};
                distribution_ptc[String(distributionKeys[i])] = {};
                dcount_ptc[String(distributionKeys[i])] = {};
            }

            const coverage_limits: { [key: string]: { moop: Limit, deductible: Limit, coinsurance: number } } = {}
            const coverage_premiums: { [key: string]: number } = {}
            /** set coverage_scores objects beginning with each premium */
            for (let i = 0; i < coverages.length; i++) {
                const coverage = coverages[i];
                coverage_limits[coverage._id] = coverage.acaPlan ? getAcaOop({policy: coverage}) : getPrivateOop({policy: coverage});
                const premium = getCoverageRate({coverage, enrolled}) * 12
                coverage_premiums[coverage._id] = premium;
                const aptc = coverage.acaPlan ? Math.min((ctx.result.aptc || 0) * 12, premium) : 0
                const eligible = coverage.acaPlan ? Math.min((coverage.aptc_eligible_premium || Infinity) * 12, premium) : 0
                coverage_scores[coverage._id] = {...coverage_scores[coverage._id], premium}
                coverage_scores[coverage._id].aptc = aptc
                coverage_scores[coverage._id].aptc_eligible_premium = eligible

                /**PTC init */
                coverage_scores_ptc[coverage._id] = {...coverage_scores_ptc[coverage._id], premium}
                coverage_scores_ptc[coverage._id].aptc = aptc
                coverage_scores_ptc[coverage._id].aptc_eligible_premium = eligible

            }

            const simsCount = 100
            let allSpend = 0;
            const byYear: number[] = []

            const spendList: Array<number> = []
            /** run sims */
            for (let i = 0; i < simsCount; i++) {
                const {spend, byCoverageId} = simulateHealthcareSpend({
                    household,
                    coverages,
                    key,
                    risk,
                    coverage_limits
                })

                allSpend += spend;
                byYear.push(spend);
                let spent = false;
                for (let s = 0; s < spendList.length; s++) {
                    if (spend < spendList[s]) {
                        spendList.splice(s, 0, spend);
                        spent = true;
                        break;
                    }
                }
                if (!spent) spendList.push(spend);


                let distribution_key = '*';
                for (let di = 0; di < distributionKeys.length; di++) {
                    if (spend <= distributionKeys[di]) {
                        distribution_key = String(distributionKeys[di])
                        break;
                    }
                }

                const best_worst: Array<[string, number]> = [];
                const best_worst_ptc: Array<[string, number]> = [];
                for (const k in byCoverageId) {
                    const oop = byCoverageId[k];
                    const total = oop + coverage_scores[k].premium;
                    const total_ptc = oop + Math.max(coverage_scores[k].premium - Math.min(coverage_scores[k].aptc, coverage_scores[k].aptc_eligible_premium), 0);

                    /** Log distribution for later setting averages */
                    distribution[distribution_key][k] = (distribution[distribution_key][k] || 0) + total;
                    dcount[distribution_key][k] = (dcount[distribution_key][k] || 0) + 1;
                    distribution_ptc[distribution_key][k] = (distribution_ptc[distribution_key][k] || 0) + total_ptc;
                    dcount_ptc[distribution_key][k] = (dcount_ptc[distribution_key][k] || 0) + 1;

                    /** AVERAGE & LOGS */
                    coverage_scores[k].average = (coverage_scores[k].average || 0) + total
                    /** PTC AVERAGE & LOGS */
                    coverage_scores_ptc[k].average = (coverage_scores_ptc[k].average || 0) + total_ptc
                    /** END AVERAGE & LOGS */

                    /** LOG SPEND TOTALS */
                    best_worst.push([k, total])
                    best_worst_ptc.push([k, total_ptc])

                }

                /** Loop through and set ranks by ID to avoid looping through to find rank index on every key */
                const ranks: any = {};
                const ranks_ptc: any = {};

                for (let idx = 0; idx < best_worst.sort((a, b) => a[1] - b[1]).length; idx++) {
                    ranks[best_worst[idx][0]] = idx
                }
                for (let idx = 0; idx < best_worst_ptc.sort((a, b) => a[1] - b[1]).length; idx++) {
                    ranks_ptc[best_worst_ptc[idx][0]] = idx
                }

                /** Loop back through keys and set scores according to rank */
                for (const k in byCoverageId) {
                    const rank = ranks[k];
                    const rank_ptc = ranks_ptc[k];

                    const ten_percent = Math.max(3, Math.ceil(best_worst.length * .1))
                    /** Top 3 values */

                    if (rank < ten_percent) {
                        coverage_scores[k].top3 = (coverage_scores[k].top1 || 0) + 1
                        if (rank === 0) {
                            coverage_scores[k].top1 = (coverage_scores[k].top1 || 0) + 1
                        }
                    }


                    if (rank_ptc < ten_percent) {
                        coverage_scores_ptc[k].top3 = (coverage_scores_ptc[k].top1 || 0) + 1

                        /** Top 3 PTC */
                        if (rank_ptc === 0) {
                            coverage_scores_ptc[k].top1 = (coverage_scores_ptc[k].top1 || 0) + 1
                        }
                    }
                    /** END Top 3 */

                    /** Last */

                    const last3 = best_worst.length - ten_percent;
                    if (rank >= last3) {
                        coverage_scores[k].last = (coverage_scores[k].last || 0) + 1
                    }

                    if (rank_ptc >= last3) {
                        coverage_scores_ptc[k].last = (coverage_scores_ptc[k].last || 0) + 1
                    }

                }

            }
            /**loop through distribution keys and average total*/
            for (const breakpoint in distribution) {
                for (const k in distribution[breakpoint]) {
                    if (isNaN(distribution[breakpoint][k])) {
                        distribution[breakpoint][k] = 0;
                    } else distribution[breakpoint][k] = Math.round(distribution[breakpoint][k] / dcount[breakpoint][k])
                }
                for (const k in distribution_ptc[breakpoint]) {
                    if (isNaN(distribution_ptc[breakpoint][k])) {
                        distribution_ptc[breakpoint][k] = 0;
                    } else distribution_ptc[breakpoint][k] = Math.round(distribution_ptc[breakpoint][k] / dcount_ptc[breakpoint][k])

                }
            }
            /**loop through and divide average total by sims for actual average */
            for (const k in coverage_scores) {
                coverage_scores[k].average = Math.round(coverage_scores[k].average / simsCount);
                coverage_scores_ptc[k].average = Math.round(coverage_scores_ptc[k].average / simsCount);
            }

            const defSpend = () => {
                return {spend: 0, count: 0}
            }
            const spend_dist = {
                '1': defSpend(),
                '5': defSpend(),
                '10': defSpend(),
                '20': defSpend(),
                '50': defSpend(),
                '0': defSpend()
            };
            const spendListLength = spendList.length;
            for (const k of [1, 5, 10, 20, 50]) {
                const percent = Math.ceil(spendListLength * (k / 100))
                const arr = spendList.slice(-percent)
                spend_dist[String(k)] = {spend: Math.round(arr.reduce((acc, v) => v + acc)), count: arr.length}
            }
            spend_dist['0'] = {
                count: spendListLength - spend_dist['50'].count,
                spend: allSpend - spend_dist['50'].spend
            }

            const spend = allSpend / simsCount;

            // const getSpend = (spend:number, pKey:string, { moop, coinsurance, deductible, familyAcc }:{moop:Limit, deductible:Limit, familyAcc:number, coinsurance?:number }) => {
            //     if (pKey === 'single') {
            //         const ded = Math.min(spend, deductible.single || deductible.family)
            //         const coins = (spend - ded) * (Number(coinsurance || 0) <= 1 ? Number(coinsurance) : 0)
            //         return Math.min(moop.single || moop.family, ded + coins)
            //     } else {
            //         const spd = spend;
            //         const familyDed = Math.max(0, deductible.family - (familyAcc || 0));
            //         const ded = Math.min(spd, deductible.single, familyDed)
            //         const coins = (spd - ded) * (Number(coinsurance) <= 1 ? Number(coinsurance) : 0)
            //         const familyMoop = (moop.family || moop.single) - (familyAcc || 0);
            //         return Math.max(0, Math.min(ded + coins, (moop.single || moop.family), familyMoop))
            //     }
            // }
            let tax_rate = (cost_sim.tax_rate || 0);
            try {
                const hh_members = household.people
                const tax_obj = quickTaxTotal({
                    income: household.income,
                    hh_members: hh_members.map(a => {
                        return {
                            age: a.age,
                            dependent: a.child || a.age < 18,
                            dob: a.dob
                        }
                    }),
                    filing_as: hh_members.some(a => a.relation === 'spouse') ? 'mj' : hh_members.length > 1 ? 'hh' : 's'
                })
                tax_rate = Math.max(0, tax_obj.rate);
                if(isNaN(tax_rate)) tax_rate = .06;
            } catch (e:any){
                console.log(`Error calculating tax rate in cost sim: ${e.message}`)
            }
            /** add deductible and oop @ average spend*/
            for (let i = 0; i < coverages.length; i++) {
                let use_tax_rate = tax_rate + 0;
                if(coverages[i].type === 'hs') use_tax_rate = 0;
                const premium = coverage_premiums[coverages[i]._id];
                const tax_savings = Math.max(0, premium * (use_tax_rate + .153))
                const premium_ptc = coverages[i].off_exchange ? premium : Math.max(premium - (ctx.result.aptc || 0) * 12, 0);
                const average = coverage_scores[coverages[i]._id].average
                const total = average - tax_savings;
                const total_ptc = coverage_scores_ptc[coverages[i]._id].average;
                coverage_scores[coverages[i]._id] = {
                    ...coverage_scores[coverages[i]._id],
                    spend,
                    oop: average - premium,
                    oop_ptc: total_ptc - premium_ptc,
                    total,
                    total_ptc,
                    premium,
                    tax_savings,
                    premium_ptc: Math.min(premium, premium_ptc),
                    ...coverage_limits[coverages[i]._id],
                }
            }

            const aca_issuers: string[] = [];
            const facets = [...ctx.result.gold?.facet_groups || [], ...ctx.result.silver?.facet_groups || [], ...ctx.result.bronze?.facet_groups || []]
            for (let i = 0; i < facets.length; i++) {
                if (facets[i].name === 'issuers') {
                    for (let idx = 0; idx < facets[i].facets.length; idx++) {
                        aca_issuers.push(facets[i].facets[idx].value);
                    }
                }
            }

            const aCa_issuers = Array.from(new Set(aca_issuers))


            const data = {
                ...cost_sim.data,
                limit_remaining: ctx.result.limit_remaining || 1000,
                issuers: private_policies.data.map(a => a.issuer).filter(a => !!a),
                aca_issuers: aCa_issuers,
                person: shopData.person ? shopData.person : undefined,
                enrollment: shopData.enrollment ? shopData.enrollment : undefined,
                plan: shopData.plan ? shopData.plan : undefined,
                spend_dist,
                allSpend,
                byYear,
                worst10: spendList.slice(-10),
                distribution,
                distribution_ptc,
                coverage_scores,
                coverage_scores_ptc,
                spend,
                tax_rate,
                lastRun: new Date(),
                simsCount,
                coverages: coverages.map(a => {
                    return {
                        _id: a._id,
                        name: a.name,
                        carrierName: a.carrierName,
                        carrierLogo: a.carrierLogo,
                        acaPlan: a.acaPlan,
                        type: a.type,
                        off_exchange: a.off_exchange,
                        on_exchange: a.on_exchange,
                        plan_id: a.plan_id,
                        metal: a.metal,
                        deductible: a.deductible,
                        moop: a.moop,
                        coinsurance: a.coinsurance,
                        copys: a.copays,
                        coins: a.coins,
                        benefits: a.benefits
                    }
                }),
                stats: cost_sim.stats,
                useAptc: !cost_sim.skip_aptc,
                skipAptc: cost_sim.skip_aptc,
                compare_ids: cost_sim.compare_ids || undefined
            }
            if (ctx.result.aptc || ctx.result.aptc === 0) data.aptc = ctx.result.aptc
            if (ctx.result.slcsp?.premium || ctx.result.slcsp?.premium === 0) data.slcsp = ctx.result.slcsp.premium
            if (context.id === fakeId) {
                context.result = await new CoreCall('shops', context).create(data)
            } else context.result = await new CoreCall('shops', context).patch(context.id as any, data)
        }
        return context;
    }
}

