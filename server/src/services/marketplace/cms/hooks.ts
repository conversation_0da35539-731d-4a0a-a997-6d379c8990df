import {HookContext} from '../../../declarations.js';
import axios from 'axios';
import {nonMarketplaceStates, normalizeCmsPolicy} from './index.js';
import {getAptc, getSlcsp} from '../utils/hooks.js';
import { NormPolicy } from '../utils';
import {stateExchangeGet, stateExchangeSlcsp, stateExchangeSearch} from '../state-exchange/index.js';
import {find_ptc_with_income_and_slcsp} from '../utils/aca.js';

type SlcspOptions = {
    household: { income: number, people: Array<any> },
    place: { state: string, countyfips: string, zipcode: string }
}


export const cmsDateFormat = (date:any) => {
    let dt = date;
    try {
        dt = new Date(date).toISOString().split("T")[0]
    } catch(e:any){
        console.log(`Error creating cms date: ${e.message}`)
    }
    return dt;
}


export const getAge = (dob:string) => {
    const today = new Date(); // Get today's date
    const birthDate = new Date(dob); // Convert input to a Date object

    let age = today.getFullYear() - birthDate.getFullYear(); // Calculate the year difference

    // Adjust age if the birthday hasn't occurred yet this year
    const hasHadBirthday =
        today.getMonth() > birthDate.getMonth() ||
        (today.getMonth() === birthDate.getMonth() && today.getDate() >= birthDate.getDate());

    if (!hasHadBirthday) {
        age--;
    }

    return age;
}

// Example Usage

export const getCmsPerson = (ee: any):any => {
    const smoke = ee.smoker || (ee.monthsSinceSmoked && ee.monthsSinceSmoked < 12);
    const age = ee.age || ee.age === 0 ? ee.age : getAge(ee.dob)
    return {
        aptc_eligible: true,
        uses_tobacco: !!smoke,
        gender: ee.gender ? ee.gender.charAt(0).toUpperCase() + ee.gender.slice(1) : 'Male',
        age,
        child: ee.relation === 'child' || ee.child || age < 18
    }
}


export const convertCmsPerson = (p: any) => {
    const dob = cmsDateFormat(p.dob);
    const age = p.age || p.age === 0 ? p.age : getAge(p.dob);
    return {
        age,
        dob,
        child: !!(p.dependent || p.relation === 'child' || age < 18),
        smoker: !!(p.smoker || (p.monthsSinceSmoked || 100) < 12),
        gender: p.gender?.toLowerCase() === 'male' ? 'Male' : 'Female'
    }
}

export const genCmsHousehold = (stats: any) => {
    // let people = stats.people;
    // if (!people?.length) {
    const people = [getCmsPerson({ age: stats.age, relation: 'self', smoker:!!stats.smoker, gender: stats.gender}), ...(stats.people || []).filter(a => !a.inactive).map(a => convertCmsPerson(a))]
    // if (stats.spouse) people.push(getCmsPerson(stats.age, false, !!stats.smoker));
    // for (let i = 0; i < stats.plus; i++) {
    //     people.push(getCmsPerson(10, true, false))
    // }
    // } else {
    //     /** set stats from people array */
    //     let self = false;
    //     for (let i = 0; i < people.length; i++) {
    //         const { age, child } = people[i];
    //         if (age > stats.age) stats.age = age;
    //         if (age > 18 && !child) {
    //             if (!self) self = true;
    //             else stats.spouse = true;
    //         } else stats.plus = (stats.plus || 0) + 1;
    //     }
    // }
    return {income: stats.income, people, place: stats.place}
}


export const getCmsSlcsp = ({household, place}: SlcspOptions) => {
    return async (context: HookContext) => {
        const {marketplace_key} = context.app.get('cms') as any

        if(nonMarketplaceStates.includes(place.state)) return await stateExchangeSlcsp({ household, place, business_year: context.params.query?.business_year })(context)
        const body = {household, place}
        const headers = {
            'apikey': marketplace_key,
            'Content-Type': 'application/json'
        }
        return await axios.post(`https://marketplace.api.healthcare.gov/api/v1/households/slcsp?apikey=${marketplace_key}`, {
            ...body,
            household: {...household, people: household.people.map(a => getCmsPerson(a))}
        }, {headers})
            .catch(err => {
                console.log(`Error fetching slcsp data`, err.message);
                throw new Error(err.message);
            })
    }
}

export const getAptcEstimate = ({household, place}) => {
    return async (context: HookContext) => {
        const {marketplace_key} = context.app.get('cms') as any

        if(nonMarketplaceStates.includes(place.state)){
            const slcsp = await stateExchangeSlcsp({ household, place, business_year: context.params.query?.business_year })(context)
            return find_ptc_with_income_and_slcsp({
                income: household.income,
                members: household.people.length,
                slcsp: slcsp?.premium * 12
            })
        }
        const headers = {
            'apikey': marketplace_key,
            'Content-Type': 'application/json'
        }
        const est = await axios.post(`https://marketplace.api.healthcare.gov/api/v1/households/eligibility/estimates?apikey=${marketplace_key}`, {
            place,
            household: {...household, people: household.people.map(a => getCmsPerson(a))}
        }, {headers})
            .catch(err => {
                console.log(`Error fetching slcsp data`, err.message);
                throw new Error(err.message);
            })
        return est.data.estimates[0]?.aptc || 0;
    }
}

const getBody = (filter: any) => {
    return (context: HookContext) => {
        return {
            filter,
            // "household": {
            // "income": 0,
            // "unemployment_received": "Adult",
            // "people": [], // see person
            // "has_married_couple": false,
            // "effective_date": ""
            // },
            // "offset": 0,
            // "order": "asc",
            // "place": {
            //     countyfips*	string
            // 5-digit county FIPS code
            //
            // state*	string
            // 2-letter USPS state abbreviation
            //
            // zipcode*	string
            // 5-digit ZIP Code
            // },
            // "sort": "premium",
            // "year": 0,
            "market": context.params.query.market || "Individual", //[ Individual, SHOP, Any ]
            // "aptc_override": 0,
            // "csr_override": "CSR73",
            // "catastrophic_override": false,
            // "suppressed_plan_ids": [],
            "place": context.params.query.place,
            "limit": 3,
            "offset": context.params.query.$skip || 0
        }
    }
}

const getHeaders = (context: HookContext) => {
    const {marketplace_key} = context.app.get('cms') as any
    return {
        'apikey': marketplace_key,
        'Content-Type': 'application/json'
    }
}
//
const getUrl = (suffix: string, params?: any) => {
    return (context: HookContext): string => {
        const {marketplace_key} = context.app.get('cms') as any
        //params might be limit or sort=premium:asc
        let p = `?apikey=${marketplace_key}`
        for (const k in params || {}) {
            p += `&${k}=${params[k]}`
        }
        return `https://marketplace.api.healthcare.gov/api/v1/${suffix}${p}`
    }
}
const pickPolicy = (payload: any) => {
    if (Array.isArray(payload)) {
        return payload.map(a => normalizeCmsPolicy(a))
    } else if(payload) return normalizeCmsPolicy(payload)
    return;
}
const setData = (res: any, ex:any) => {
    const {
        plans,
        total,
        // rate_area,
        // ranges,
        facet_groups,
        // benefits
    } = res.data || {}
    return {data: [...pickPolicy(plans) as [], ...ex?.data || []], total: total + (ex?.total || 0), facet_groups}
}

export const cmsMarketplaceGet = (id:string|Array<string>, household:any, place:any, options?:{ throw?:boolean, filter?:any }) => {
    return async (context: HookContext) => {
        const ids = Array.isArray(id) ? id : [id]

        if(nonMarketplaceStates.includes(place?.state || household.place.state)){
            const { query } = context.params;
            query.household = household;
            query.place = place || household.place;
            query.id = ids
            return await stateExchangeGet(context)
        }
        const headers = getHeaders(context);
        const url:string = getUrl('plans')(context);
        const body:any = getBody(options?.filter)(context)
        body.plan_ids = ids;
        body.household = { ...household, people: household.people.map(a => getCmsPerson(a))}
        body.place = place;
        // body.limit = ids.length;
        const res:any = await axios.post(url, {...body},{headers})
            .catch(err => {
                console.log(`Error getting marketplace data`, err.message, err.response.data);
                if (options?.throw) throw new Error(err.message);
                else return { data: {} }
            })
        const formatted = pickPolicy(res.data.plans);
        return formatted as Array<NormPolicy>;
    }
}

export const cmsMarketplaceSearch = async (context: HookContext) => {
    const {quick_quote = {}} = context.params.runJoin || {}
    const filter = {
        metal_levels: ['Bronze', 'Silver', 'Gold'],
        limit: quick_quote?.limit || context.params.query?.$limit || 5
    }
    const body: any = getBody(filter)(context);

    if(nonMarketplaceStates.includes(body.place.state)) return await stateExchangeSearch(context)

    body.limit = 1;
    const headers = getHeaders(context);

    const {filter:fltr, household} = context.params.query;
    if (household) body.household = {...household, people: household.people.map(a => getCmsPerson(a))};
    if (fltr) body.filter = {...body.filter, ...fltr}
    body.limit = filter.limit || 5;
    delete filter.limit;
    const offset = context.params.query?.$skip || 0;
    body.offset = offset;
    const run = async (mods?: any, noThrow?: boolean): Promise<any> => {
        return await axios.post(getUrl('plans/search', {})(context), {...body, ...mods}, {headers})
            .catch(err => {
                console.log(`Error fetching marketplace data`, err.message, err.response.data);
                if (!noThrow) throw new Error(err.message);
                else return {}
            })
    }
    context.result = { ...context.result }

    const skips = { bronze: offset + 0, silver: offset + 0, gold: offset + 0 }
    const mods = { bronze: {filter: { ...body.filter, metal_level: 'Bronze' }}, silver: {filter: { ...body.filter, metal_level: 'Silver' }}, gold: {filter: { ...body.filter, metal_level: 'Gold' }} }
    const {bronze: bDeds, silver: sDeds, gold: gDeds} = quick_quote?.deds || {};
    const bFilter = {...body.filter, metal_level: 'Bronze'}
    // if (bDeds) bFilter.deductible_range = bDeds
    const loopPath = async (path:string, ex?:any) => {
        const fetched = await run(mods[path], true)
        const result = setData(fetched, ex)
        if(fetched.data?.total > (body.limit || 5) + (skips[path] || 0)){
            skips[path] += fetched.data?.plans?.length;
            mods[path].offset = skips[path];
            return await loopPath(path, result);
        }
        return {result, limit_remaining: Number((fetched.headers || {})['x-ratelimit-remaining-minute'] || 1000)};
    }
    const { result: bronzes } = await loopPath('bronze');
    const { result: silvers } = await loopPath('silver');
    const { result:golds, limit_remaining } = await loopPath('gold');
    context.result.bronze = bronzes;
    context.result.silver = silvers;
    context.result.gold = golds;
    context.result.limit_remaining = limit_remaining;


    const def = () => {
        return {
            data: [],
            total: 0
        }
    }
    const {bronze = def(), silver = def(), gold = def()} = context.result
    const aptc = await getAptc({household: body.household, household_size: context.params.query?.household_size, place: body.place, silver_plans: silver.data})(context)
    const slcsp: any = await getSlcsp({
        household: body.household,
        place: body.place
    })(context)
        .catch(err => {
            console.log(`Couldn't get slcsp: ${err.message}`)
            return {}
        })

    context.result = {
        ...context.result,
        facet_groups: context.result.bronze?.data?.facet_groups || [],
        slcsp: slcsp?.data,
        aptc,
        limit: context.params.query.$limit,
        skip: context.params.query.$skip,
        data: [],
        total: gold.total + silver.total + bronze.total
    }

    return context;
}
//
// // A configure function that registers the service and its hooks via `app.configure`
// export const marketplace = (app: Application) => {
//     // Register our service on the Feathers application
//     app.use(marketplacePath, new MarketplaceService(getOptions(app)), {
//         // A list of all methods this service exposes externally
//         methods: marketplaceMethods,
//         // You can add additional custom events to be sent to clients here
//         events: []
//     })
//     // Initialize hooks
//     app.service(marketplacePath).hooks({
//         around: {
//             all: []
//         },
//         before: {
//             all: [],
//             find: [],
//             get: [],
//             create: [],
//             patch: [],
//             remove: []
//         },
//         after: {
//             all: [],
//             find: [searchPlans, quickQuote]
//         },
//         error: {
//             all: []
//         }
//     })
// }
//
// // Add this service to the service type index
// declare module '../../declarations.js' {
//     interface ServiceTypes {
//         [marketplacePath]: MarketplaceService
//     }
// }

// export const searchPlans = async (context: HookContext): Promise<HookContext> => {
//     if (!context.params.runJoin?.quick_quote) {
//         const filter = {
//             metal_levels: ['Bronze', 'Silver', 'Gold', 'Platinum']
//             // "disease_mgmt_programs": [],//[ Asthma, Heart Disease, Depression, Diabetes, High Blood Pressure and High Cholesterol, Low Back Pain, Pain Management, Pregnancy, Weight Loss Programs ]
//             // "division": "HealthCare", //enum:[HealthCare, Dental]
//             // "issuer": "",
//             // "issuers": [],
//             // "metal_levels": [], //enum:[Catastrophic, Silver, Bronze, Gold, Platinum ]
//             // "metal_level": "Catastrophic",
//             // "metal_design_types": [],
//             // "design_types": [], //enum:[DESIGN1, DESIGN2, DESIGN3, DESIGN4, DESIGN5, NOT_APPLICABLE ]
//             // "premium": 0,
//             // "type": "Indemnity",
//             // "types": [], //enum:[ Indemnity, PPO, HMO, EPO, POS ]
//             // "deductible": 0,
//             // "hsa": false,
//             // "oopc": 0,
//             // "child_dental_coverage": false,
//             // "adult_dental_coverage": false,
//             // "drugs": [],
//             // "providers": [],
//             // "quality_rating": 0,
//             // "simple_choice": false,
//             // "premium_range": {
//             //     "min": 0,
//             //     "max": 0
//             // },
//             // "deductible_range": {
//             //     "min": 0,
//             //     "max": 0
//             // }
//         };
//         const body: any = getBody(filter)(context);
//         const headers = getHeaders(context)
//         const {filter: fltr, household, drugs, providers} = context.params.query || {};
//         if (fltr) body.filter = {...body.filter, ...fltr}
//         if (household) body.household = household;
//
//         const run = async (mods?: any, noThrow?: boolean): Promise<any> => {
//             // return await axios.post(getUrl('plans/search', {
//             //     limit: context.params.query?.$limit || 10,
//             //     offset: context.params.query?.$skip || 0
//             // })(context), {...body, ...mods}, {headers})
//             await ideonPolicySearch({household: body.household, place: body.place})(context)
//                 .catch(err => {
//                     console.log(`Error fetching marketplace data`, err.message);
//                     if (!noThrow) throw new Error(err.message);
//                     else return {}
//                 })
//         }
//
//         const data = await run();
//         context.result = setData(data);
//
//         const bronzes = await run({filter: {...body.filter, metal_level: 'Bronze'}}, true)
//         context.result.bronze = setData(bronzes);
//
//         const silvers = await run({filter: {...body.filter, metal_level: 'Silver'}}, true)
//         context.result.silver = setData(silvers)
//
//         const golds = await run({filter: {...body.filter, metal_level: 'Gold'}}, true)
//         context.result.gold = setData(golds);
//
//         const platinums = await run({filter: {...body.filter, metal_level: 'Platinum'}}, true)
//         context.result.platinum = setData(platinums);
//
//         if (context.result.data?.length) {
//             const getDrugs = async (planids: string[], path: string) => {
//                 const drugids = drugs?.filter(a => !!a);
//                 if (!drugids?.length) return;
//                 if (!planids?.length || !drugids?.length) return;
//                 const drugData = await axios.get(getUrl('drugs/covered', {
//                     drugs: drugids,
//                     planids,
//                     limit: context.params.query?.$limit || 10,
//                     offset: context.params.query?.$skip || 0
//                 })(context), {headers})
//                     .catch(err => {
//                         console.log(`Error fetching drug coverage data`, err.message)
//                         return {data: {coverage: undefined}}
//                     })
//                 context.result[path].drug_coverage = drugData.data?.coverage
//             }
//
//             const getProviders = async (planids: string[], path: string) => {
//                 const providerids = providers?.filter(a => !!a);
//                 if (!providerids) return;
//                 if (!planids?.length || !providerids?.length) return;
//                 const providerData = await axios.get(getUrl('providers/covered', {
//                     providerids,
//                     planids,
//                     limit: context.params.query?.$limit || 10,
//                     offset: context.params.query?.$skip || 0
//                 })(context), {headers})
//                     .catch(err => {
//                         console.log(`Error fetching drug coverage data`, err.message)
//                         return {data: {coverage: undefined}}
//                     })
//                 context.result[path].provider_coverage = providerData.data?.coverage;
//             }
//             const setMetal = async (a) => {
//                 const planids = context.result[a].data?.map(a => a.id)
//                 await getDrugs(planids, a)
//                 await getProviders(planids, a)
//                 return;
//             }
//             await Promise.all(['bronze', 'silver', 'gold', 'platinum'].map(a => setMetal(a)))
//         }
//     }
//     return context;
// }
//

// ******THESE ARE THE HOOKS LAST WORKING NOT USING IDEON
//
// import type {Application, HookContext} from '../../declarations.js'
// import {MarketplaceService, getOptions} from './marketplace.class.js'
// import {marketplacePath, marketplaceMethods} from './marketplace.shared.js'
//
// export * from './marketplace.class.js'
// import axios from 'axios';
//
// //person: {
// // age: number,
// // dob: required without age YYYY-MM-DD
// // has_mec: boolean
// // is_parent	boolean
// // is_pregnant	boolean
// // pregnant_with: number (of babies)
// // uses_tobacco: boolean
// // last_tobacco_use_date: YYYY-MM-DD
// // gender: enum: [Male, Female]
// // utilization_level: enum: [Low, Medium, High]
// // does_not_cohabitate	boolean
// // aptc_eligible	boolean
// // current_enrollment: {
// //     description:
// //         Current/existing enrollment information used to determine tobacco status for CiC enrollments. This will ensure rate calculation is done correctly.
// //
// //         plan_id*	PlanIDstring
// //     pattern: ^[0-9]{5}[A-Z]{2}[0-9]{7}$
// //     properties: OrderedMap {}
// //     effective_date*	string
// //     pattern: ^[0-9]{4}-[0-9]{2}-[0-9]{2}$
// //     Date plan went into effect (ISO-8601 YYYY-MM-DD)
// //
// //     uses_tobacco*	boolean
// // }
// // }
//
// // [CostSharing{
// //     coinsurance_options	string
// //     coinsurance_rate	number($float)
// //     copay_amount	number
// //     copay_options	string
// //     network_tier	NetworkTierEnumstring
// //     properties: OrderedMap {}
// //     Enum:
// // [ In-Network, In-Network Tier 2, Out-of-Network, Combined In-Out of Network ]
// // csr	CostSharingReductionEnumstring
// // properties: OrderedMap {}
// // Cost-sharing reduction (CSR)
// //
// // Enum:
// //     [ Exchange variant (no CSR), Zero Cost Sharing Plan Variation, Limited Cost Sharing Plan Variation, 73% AV Level Silver Plan CSR, 87% AV Level Silver Plan CSR, 94% AV Level Silver Plan CSR, Non-Exchange variant, Unknown CSR ]
// // display_string	string
// // }]
