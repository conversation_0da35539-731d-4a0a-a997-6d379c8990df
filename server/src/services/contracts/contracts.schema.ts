// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch, commonQueries, mandate, updatesSchema} from '../../utils/common/schemas.js';
import {sectionsSchema} from '../plan-docs/plan-docs.schema.js';

const paySchema = {
    type: 'object',
    properties: {
        fee: {type: 'number'},
        feeType: {type: 'string', enum: ['alg', 'pepm', 'pmpm', 'flat']},
        feeDescription: {type: 'string'},
        hostSplitType: {type: 'string', enum: ['percent', 'flat', 'pepm', 'pmpm']},
        hostSplitAmount: {type: 'number'},
        refSplit: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object',
                    properties: {
                        percent: {type: 'number'},
                        ref: ObjectIdSchema()
                    }
                }
            }
        }
    }
} as const
// Main data model schema
export const contractsSchema = {
    $id: 'Contracts',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name'],
    properties: {
        _id: ObjectIdSchema(),
        public: { type: 'boolean' },
        template: {type: 'boolean'},
        subject: ObjectIdSchema(),
        subjectService: { type: 'string' },
        name: { type: 'string' },
        description: { type: 'string' },
        owner: ObjectIdSchema(),
        ownerService: { type: 'string' },
        managers: { type: 'array', items: ObjectIdSchema() },
        tags: { type: 'array', items: {type: 'string'} },
        status: { type: 'string', enum: ['open', 'sent', 'rejected', 'executed']},
        rejectedBy: updatesSchema,
        pTags: { type: 'array', items: {type: 'string'} },
        parties: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        relationship: { type: 'string' },
                        by: { type: 'string' },
                        byTitle: { type: 'string' },
                        id: ObjectIdSchema(),
                        idService: {type: 'string'},
                        aka: { type: 'string' },
                        tag: {type: 'string'},
                        legalName: {type: 'string'},
                        address: {type: 'string'},
                        email: { type: 'string' },
                        phone: { type: 'string' },
                        ack: mandate
                    }
                }
            }
        },
        meta: { type: 'object', additionalProperties: true, properties: {pay: paySchema}},
        sections: sectionsSchema,
        ...commonFields.properties
    }
} as const
export type Contracts = FromSchema<typeof contractsSchema>
export const contractsValidator = getValidator(contractsSchema, dataValidator)
export const contractsResolver = resolve<Contracts, HookContext>({
    status: async (val) => {
        if(!val) return 'open'
        return val;
    }
})

export const contractsExternalResolver = resolve<Contracts, HookContext>({})

// Schema for creating new data
export const contractsDataSchema = {
    $id: 'ContractsData',
    type: 'object',
    additionalProperties: false,
    required: ['name'],
    properties: {
        ...contractsSchema.properties
    }
} as const
export type ContractsData = FromSchema<typeof contractsDataSchema>
export const contractsDataValidator = getValidator(contractsDataSchema, dataValidator)
export const contractsDataResolver = resolve<ContractsData, HookContext>({})

// Schema for updating existing data
export const contractsPatchSchema = {
    $id: 'ContractsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...contractsSchema.properties,
        ...commonPatch({ ...contractsSchema.properties }).properties
    }
} as const
export type ContractsPatch = FromSchema<typeof contractsPatchSchema>
export const contractsPatchValidator = getValidator(contractsPatchSchema, dataValidator)
export const contractsPatchResolver = resolve<ContractsPatch, HookContext>({})

// Schema for allowed query properties
export const contractsQuerySchema = {
    $id: 'ContractsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax(contractsSchema.properties),
        ...commonQueries.properties,
        tags: {},
        name: {},
        description: {}
    }
} as const
export type ContractsQuery = FromSchema<typeof contractsQuerySchema>
export const contractsQueryValidator = getValidator(contractsQuerySchema, queryValidator)
export const contractsQueryResolver = resolve<ContractsQuery, HookContext>({})
